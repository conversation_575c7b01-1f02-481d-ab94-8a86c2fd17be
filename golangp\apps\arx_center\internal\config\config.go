package config

import (
	"fmt"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/file"
	"github.com/spf13/viper"
)

type ConfigType struct {
	Mode string `mapstructure:"MODE"`
	Port string `mapstructure:"PORT"`

	// === Infurastructure ===

	DBHost         string `mapstructure:"POSTGRES_HOST"`
	DBUserName     string `mapstructure:"POSTGRES_USER"`
	DBUserPassword string `mapstructure:"POSTGRES_PASSWORD"`
	DBName         string `mapstructure:"POSTGRES_DB"`
	DBPort         string `mapstructure:"POSTGRES_PORT"`

	EmailFrom string `mapstructure:"EMAIL_FROM"`
	SMTPHost  string `mapstructure:"SMTP_HOST"`
	SMTPPass  string `mapstructure:"SMTP_PASS"`
	SMTPPort  int    `mapstructure:"SMTP_PORT"`
	SMTPUser  string `mapstructure:"SMTP_USER"`

	OSSHost      string `mapstructure:"OSS_HOST"`
	OSSSecure    bool   `mapstructure:"OSS_SECURE"`
	OSSAccessKey string `mapstructure:"OSS_ACCESS_KEY"`
	OSSSecretKey string `mapstructure:"OSS_SECRET_KEY"`
	OSSBucket    string `mapstructure:"OSS_BUCKET"`
	// Proxy S3 requests through the backend
	// so it can be accessed from the frontend even when S3 service is not public
	OSSProxy bool `mapstructure:"PROXY_MINIO"`

	AiScientistHost string `mapstructure:"AI_SCIENTIST_HOST"`

	// === Auth ===
	Domain             string `mapstructure:"DOMAIN"`
	ClientOrigin       string `mapstructure:"CLIENT_ORIGIN"` // Target origin for email verification
	TokenSecret        string `mapstructure:"TOKEN_SECRET"`
	AccessTokenMaxAge  int    `mapstructure:"ACCESS_TOKEN_MAXAGE"`  // AccessTokenMaxAge in Minutes
	RefreshTokenMaxAge int    `mapstructure:"REFRESH_TOKEN_MAXAGE"` // RefreshTokenMaxAge in Minutes

	AdminEmail    []string `mapstructure:"ADMIN_EMAIL"`
	AdminPassword string   `mapstructure:"ADMIN_PASSWORD"`
	APISecret     string   `mapstructure:"API_SECRET"` // Secret for API access, used for authentication

	// Legacy Configurations
	ApiKey string `mapstructure:"API_KEY"`
	ApiURL string `mapstructure:"API_URL"`
	Listen string `mapstructure:"LISTEN"`
	Proxy  string `mapstructure:"PROXY"`

	RedisHost     string `mapstructure:"REDIS_HOST"`
	RedisPassword string `mapstructure:"REDIS_PWD"`
	RedisDB       int    `mapstructure:"REDIS_DB"`

	MeiliHost string `mapstructure:"MEILI_HOST"`
	MeiliKey  string `mapstructure:"MEILI_KEY"`

	GiteaHost          string `mapstructure:"GITEA_HOST"`
	GiteaAdminUser     string `mapstructure:"GITEA_ADMIN_USER"`
	GiteaAdminPassword string `mapstructure:"GITEA_ADMIN_PASSWORD"`

	DifyConsoleEmail    string `mapstructure:"DIFY_CONSOLE_UMAIL"`
	DifyConsolePassword string `mapstructure:"DIFY_CONSOLE_PASSWORD"`
	DifyHost            string `mapstructure:"DIFY_HOST"`
	DifyKey             string `mapstructure:"DIFY_KEY"`
}

var Config ConfigType

func LoadConfig(path string) error {

	if path == "" {
		path = "configs/settings.yml"
	}

	return loadConfig(path, &Config)
}

func loadConfig(path string, config *ConfigType) error {
	if path == "" {
		path = "configs/settings.yml"
	}

	viper.AddConfigPath(".")
	configPath, err := file.LocateFile(path)
	if err != nil {
		return fmt.Errorf("config file not found: %w", err)
	}
	viper.SetConfigFile(configPath)
	err = viper.ReadInConfig()
	if err != nil {
		return fmt.Errorf("error reading config file: %w", err)
	}
	if err := viper.Unmarshal(&Config); err != nil {
		return fmt.Errorf("error unmarshalling config: %w", err)
	}
	fmt.Printf("Config file loaded: %s\n", configPath)
	return nil
}
