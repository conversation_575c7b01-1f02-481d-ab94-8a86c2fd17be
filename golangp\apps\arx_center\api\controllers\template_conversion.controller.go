package controllers

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/models"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/dify"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/logger"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/template_tools"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/xminio"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/ziputils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/minio/minio-go/v7"
	"gorm.io/gorm"
)

const (
	TEMPLATE_FILES_PATH        = "template-files/"
	CONVERSION_FILES_PATH      = "conversion-files/"
	TEMP_DIR_PREFIX            = "template_conversion_"
	MAX_TEMPLATE_SIZE_MB       = 50
	MAX_SOURCE_SIZE_MB         = 100
	DIFY_APP_KEY               = "app-aDvCsdCrTwmZVpYnk7FnuAKC" // TODO: move to config file
	MAX_AI_CONVERSATION_ROUNDS = 20                             // 最大AI对话轮次
	AI_RESPONSE_TIMEOUT        = 2 * time.Minute                // AI响应超时时间
	CONVERSATION_TIMEOUT       = 30 * time.Minute               // 整个对话超时时间
)

type TemplateConversionController struct {
	DB *gorm.DB
	S3 *xminio.S3Manager
}

func NewTemplateConversionController(DB *gorm.DB, S3 *xminio.S3Manager) TemplateConversionController {
	return TemplateConversionController{
		DB: DB,
		S3: S3,
	}
}

// UploadTemplate handles template file upload
func (tc *TemplateConversionController) UploadTemplate(ctx *gin.Context) {
	currentUser := ctx.MustGet("currentUser").(models.User)

	// Get file from request
	file, header, err := ctx.Request.FormFile("template")
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "Failed to get template file from request"})
		return
	}
	defer file.Close()

	// Validate file size
	if header.Size > MAX_TEMPLATE_SIZE_MB*1024*1024 {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"message": fmt.Sprintf("Template file too large. Maximum size is %d MB", MAX_TEMPLATE_SIZE_MB),
		})
		return
	}

	// Calculate file hash
	hash := sha256.New()
	if _, err := io.Copy(hash, file); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to calculate file hash"})
		return
	}
	fileHash := hex.EncodeToString(hash.Sum(nil))

	// Reset file pointer
	if _, err := file.Seek(0, 0); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to process file"})
		return
	}

	// Validate ZIP file
	tempFile, err := os.CreateTemp("", "template_*.zip")
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to create temporary file"})
		return
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	if _, err := io.Copy(tempFile, file); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to save temporary file"})
		return
	}

	if err := ziputils.ValidateZipFile(tempFile.Name()); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": fmt.Sprintf("Invalid ZIP file: %v", err)})
		return
	}

	// Get ZIP file info
	zipInfo, err := ziputils.GetZipInfo(tempFile.Name())
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to analyze ZIP file"})
		return
	}

	if !zipInfo.HasTexFiles {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "Template must contain at least one .tex file"})
		return
	}

	// Parse template metadata from form
	var payload models.CreateTemplatePayload
	if err := ctx.ShouldBind(&payload); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "Invalid template metadata"})
		return
	}

	// Reset file pointer again for upload
	if _, err := file.Seek(0, 0); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to process file"})
		return
	}

	// Upload to S3
	putOpts := minio.PutObjectOptions{
		ContentType: "application/zip",
		UserMetadata: map[string]string{
			"hash":     fileHash,
			"filename": header.Filename,
		},
	}

	_, err = tc.S3.Client.PutObject(
		ctx,
		tc.S3.BucketName,
		TEMPLATE_FILES_PATH+fileHash,
		file,
		header.Size,
		putOpts,
	)

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to upload template file"})
		return
	}

	// Create template record
	template := models.Template{
		ID:          uuid.New(),
		Name:        payload.Name,
		Description: payload.Description,
		Category:    payload.Category,
		FileHash:    fileHash,
		FileSize:    header.Size,
		FileName:    header.Filename,
		Version:     payload.Version,
		Author:      payload.Author,
		License:     payload.License,
		Tags:        payload.Tags,
		IsActive:    true,
		IsPublic:    payload.IsPublic,
		OwnerID:     currentUser.ID,
		UsageCount:  0,
	}

	if err := template.CreateTemplate(tc.DB); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to save template record"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message":  "Template uploaded successfully",
		"template": template,
		"zipInfo":  zipInfo,
	})
}

// GetTemplates retrieves templates (public + user's own)
func (tc *TemplateConversionController) GetTemplates(ctx *gin.Context) {
	currentUser := ctx.MustGet("currentUser").(models.User)

	var templates []models.Template
	err := tc.DB.Where("is_public = ? OR owner_id = ?", true, currentUser.ID).
		Where("is_active = ?", true).
		Preload("Owner").
		Order("usage_count DESC, created_at DESC").
		Find(&templates).Error

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to retrieve templates"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"templates": templates,
	})
}

// GetTemplate retrieves a specific template by ID
func (tc *TemplateConversionController) GetTemplate(ctx *gin.Context) {
	templateID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "Invalid template ID"})
		return
	}

	template, err := models.GetTemplateByID(tc.DB, templateID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"message": "Template not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"template": template,
	})
}

// StartConversion initiates a template conversion task
func (tc *TemplateConversionController) StartConversion(ctx *gin.Context) {
	currentUser := ctx.MustGet("currentUser").(models.User)

	// Get source file from request
	file, header, err := ctx.Request.FormFile("source")
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "Failed to get source file from request"})
		return
	}
	defer file.Close()

	// Validate file size
	if header.Size > MAX_SOURCE_SIZE_MB*1024*1024 {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"message": fmt.Sprintf("Source file too large. Maximum size is %d MB", MAX_SOURCE_SIZE_MB),
		})
		return
	}

	// Parse conversion request
	var payload models.CreateConversionPayload
	if err := ctx.ShouldBind(&payload); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid template metadata",
			"error":   err.Error(),
		})
		return
	}

	// Validate target template exists
	template, err := models.GetTemplateByID(tc.DB, payload.TargetTemplateID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"message": "Target template not found"})
		return
	}

	// Calculate source file hash
	hash := sha256.New()
	if _, err := io.Copy(hash, file); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to calculate file hash"})
		return
	}
	sourceFileHash := hex.EncodeToString(hash.Sum(nil))

	// Reset file pointer
	if _, err := file.Seek(0, 0); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to process file"})
		return
	}

	// Validate source ZIP file
	tempFile, err := os.CreateTemp("", "source_*.zip")
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to create temporary file"})
		return
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	if _, err := io.Copy(tempFile, file); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to save temporary file"})
		return
	}

	if err := ziputils.ValidateZipFile(tempFile.Name()); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": fmt.Sprintf("Invalid source ZIP file: %v", err)})
		return
	}

	// Reset file pointer for upload
	if _, err := file.Seek(0, 0); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to process file"})
		return
	}

	// Upload source file to S3
	putOpts := minio.PutObjectOptions{
		ContentType: "application/zip",
		UserMetadata: map[string]string{
			"hash":     sourceFileHash,
			"filename": header.Filename,
		},
	}

	_, err = tc.S3.Client.PutObject(
		ctx,
		tc.S3.BucketName,
		CONVERSION_FILES_PATH+"source_"+sourceFileHash,
		file,
		header.Size,
		putOpts,
	)

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to upload source file"})
		return
	}

	// Create conversion task record
	conversion := models.TemplateConversion{
		ID:               uuid.New(),
		TaskName:         payload.TaskName,
		Status:           models.ConversionStatusPending,
		SourceFileHash:   sourceFileHash,
		SourceFileName:   header.Filename,
		SourceFileSize:   header.Size,
		TargetTemplateID: payload.TargetTemplateID,
		UserID:           currentUser.ID,
	}

	if err := conversion.CreateConversion(tc.DB); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to create conversion task"})
		return
	}

	// Increment template usage count
	if err := template.IncrementUsageCount(tc.DB); err != nil {
		logger.Warning("Failed to increment template usage count: %v", err)
	}

	// Start conversion process asynchronously
	go tc.processConversion(conversion.ID)

	ctx.JSON(http.StatusOK, gin.H{
		"message":    "Conversion task created successfully",
		"conversion": conversion,
	})
}

// GetConversions retrieves conversion tasks for the current user
func (tc *TemplateConversionController) GetConversions(ctx *gin.Context) {
	currentUser := ctx.MustGet("currentUser").(models.User)

	conversions, err := models.GetConversionsByUser(tc.DB, currentUser.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to retrieve conversions"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"conversions": conversions,
	})
}

// GetConversion retrieves a specific conversion task by ID
func (tc *TemplateConversionController) GetConversion(ctx *gin.Context) {
	currentUser := ctx.MustGet("currentUser").(models.User)

	conversionID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "Invalid conversion ID"})
		return
	}

	conversion, err := models.GetConversionByID(tc.DB, conversionID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"message": "Conversion not found"})
		return
	}

	// Check if user owns this conversion
	if conversion.UserID != currentUser.ID {
		ctx.JSON(http.StatusForbidden, gin.H{"message": "Access denied"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"conversion": conversion,
	})
}

// DownloadResult handles downloading the conversion result
func (tc *TemplateConversionController) DownloadResult(ctx *gin.Context) {
	currentUser := ctx.MustGet("currentUser").(models.User)

	conversionID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "Invalid conversion ID"})
		return
	}

	conversion, err := models.GetConversionByID(tc.DB, conversionID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"message": "Conversion not found"})
		return
	}

	// Check if user owns this conversion
	if conversion.UserID != currentUser.ID {
		ctx.JSON(http.StatusForbidden, gin.H{"message": "Access denied"})
		return
	}

	// Check if conversion is completed and has result
	if conversion.Status != models.ConversionStatusCompleted || conversion.ResultFileHash == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "Conversion result not available"})
		return
	}

	// Generate presigned URL for download
	presignedURL, err := tc.S3.Client.PresignedGetObject(
		ctx,
		tc.S3.BucketName,
		CONVERSION_FILES_PATH+"result_"+conversion.ResultFileHash,
		time.Hour*24,
		nil,
	)

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to generate download URL"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"downloadUrl": presignedURL.String(),
		"fileName":    conversion.ResultFileName,
	})
}

// processConversion handles the actual conversion process using Dify AI
func (tc *TemplateConversionController) processConversion(conversionID uuid.UUID) {
	// Get conversion record
	conversion, err := models.GetConversionByID(tc.DB, conversionID)
	if err != nil {
		logger.Danger("Failed to get conversion record: %v", err)
		return
	}

	// Mark as started
	if err := conversion.MarkAsStarted(tc.DB); err != nil {
		logger.Danger("Failed to mark conversion as started: %v", err)
		return
	}

	// Create temporary directories
	tempDir, err := os.MkdirTemp("", TEMP_DIR_PREFIX)
	if err != nil {
		tc.markConversionFailed(conversion, fmt.Sprintf("Failed to create temp directory: %v", err))
		return
	}
	defer ziputils.CleanupTempDir(tempDir)

	sourceDir := filepath.Join(tempDir, "source")
	templateDir := filepath.Join(tempDir, "template")
	resultDir := filepath.Join(tempDir, "result")

	// Download and extract source file
	if err := tc.downloadAndExtractFile(CONVERSION_FILES_PATH+"source_"+conversion.SourceFileHash, sourceDir); err != nil {
		tc.markConversionFailed(conversion, fmt.Sprintf("Failed to download source file: %v", err))
		return
	}

	// Download and extract template file
	if err := tc.downloadAndExtractFile(TEMPLATE_FILES_PATH+conversion.TargetTemplate.FileHash, templateDir); err != nil {
		tc.markConversionFailed(conversion, fmt.Sprintf("Failed to download template file: %v", err))
		return
	}

	// Copy source to result directory for processing
	if err := tc.copyDirectory(sourceDir, resultDir); err != nil {
		tc.markConversionFailed(conversion, fmt.Sprintf("Failed to copy source files: %v", err))
		return
	}

	// Initialize Dify client and start AI conversion
	if err := tc.runAIConversion(conversion, resultDir, templateDir); err != nil {
		tc.markConversionFailed(conversion, fmt.Sprintf("AI conversion failed: %v", err))
		return
	}

	// Create result ZIP file
	resultZipPath := filepath.Join(tempDir, "result.zip")
	if err := ziputils.CreateZip(resultDir, resultZipPath); err != nil {
		tc.markConversionFailed(conversion, fmt.Sprintf("Failed to create result ZIP: %v", err))
		return
	}

	// Upload result file
	if err := tc.uploadResultFile(conversion, resultZipPath); err != nil {
		tc.markConversionFailed(conversion, fmt.Sprintf("Failed to upload result: %v", err))
		return
	}

	// Mark as completed
	if err := conversion.MarkAsCompleted(tc.DB); err != nil {
		logger.Danger("Failed to mark conversion as completed: %v", err)
	}

	logger.Info("Conversion completed successfully: %s", conversion.ID.String())
}

// markConversionFailed marks a conversion as failed with error message
func (tc *TemplateConversionController) markConversionFailed(conversion *models.TemplateConversion, errorMessage string) {
	if err := conversion.MarkAsFailed(tc.DB, errorMessage); err != nil {
		logger.Warning("Failed to mark conversion as failed: %v", err)
	}
	logger.Warning("Conversion failed: %s - %s", conversion.ID.String(), errorMessage)
}

// downloadAndExtractFile downloads a file from S3 and extracts it to target directory
func (tc *TemplateConversionController) downloadAndExtractFile(s3Key, targetDir string) error {
	// Create temporary file for download
	tempFile, err := os.CreateTemp("", "download_*.zip")
	if err != nil {
		return fmt.Errorf("failed to create temp file: %w", err)
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	// Download from S3
	object, err := tc.S3.Client.GetObject(
		context.TODO(),
		tc.S3.BucketName,
		s3Key,
		minio.GetObjectOptions{},
	)
	if err != nil {
		return fmt.Errorf("failed to get object from S3: %w", err)
	}
	defer object.Close()

	// Copy to temp file
	if _, err := io.Copy(tempFile, object); err != nil {
		return fmt.Errorf("failed to download file: %w", err)
	}

	// Extract ZIP file
	if err := ziputils.ExtractZip(tempFile.Name(), targetDir); err != nil {
		return fmt.Errorf("failed to extract ZIP: %w", err)
	}

	return nil
}

// copyDirectory recursively copies a directory
func (tc *TemplateConversionController) copyDirectory(src, dst string) error {
	return filepath.Walk(src, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Get relative path
		relPath, err := filepath.Rel(src, path)
		if err != nil {
			return err
		}

		dstPath := filepath.Join(dst, relPath)

		if info.IsDir() {
			return os.MkdirAll(dstPath, info.Mode())
		}

		// Copy file
		srcFile, err := os.Open(path)
		if err != nil {
			return err
		}
		defer srcFile.Close()

		if err := os.MkdirAll(filepath.Dir(dstPath), 0755); err != nil {
			return err
		}

		dstFile, err := os.Create(dstPath)
		if err != nil {
			return err
		}
		defer dstFile.Close()

		_, err = io.Copy(dstFile, srcFile)
		return err
	})
}

// runAIConversion executes the AI-powered template conversion with timeout and round limits
func (tc *TemplateConversionController) runAIConversion(conversion *models.TemplateConversion, resultDir, templateDir string) error {
	// Create context with timeout for the entire conversation
	ctx, cancel := context.WithTimeout(context.Background(), CONVERSATION_TIMEOUT)
	defer cancel()

	// Initialize Dify client
	difyClient, err := dify.GetDifyClient()
	if err != nil {
		return fmt.Errorf("failed to initialize Dify client: %w", err)
	}

	// Initialize template tools manager
	toolsManager := template_tools.NewTemplateToolsManager(resultDir, templateDir, resultDir)

	// Create channels for communication
	responseChan := make(chan dify.ChatMessagesResponse, 1)
	errorChan := make(chan error, 1)
	statusChan := make(chan string, 10)

	// Start status update goroutine
	go tc.updateConversionStatus(conversion, statusChan)

	// Prepare initial query for AI agent
	query := fmt.Sprintf(`Please convert the LaTeX project to match the target template format.
Task: %s
Source template: %s
Target template: %s

Please analyze both the source project and target template, then perform the conversion step by step.
Use the provided tools to examine files, make modifications, and verify the conversion.`,
		conversion.TaskName,
		conversion.SourceFileName,
		conversion.TargetTemplate.Name)

	statusChan <- "Starting AI conversation..."

	// Start conversation with Dify AI in goroutine
	go func() {
		response, err := difyClient.ChatMessages(
			query,
			map[string]interface{}{
				"source_dir":   resultDir,
				"template_dir": templateDir,
			},
			"", // no conversation ID for new conversation
			[]dify.ChatMessagesPayloadFile{},
			DIFY_APP_KEY,
		)

		if err != nil {
			errorChan <- fmt.Errorf("failed to start AI conversation: %w", err)
			return
		}

		responseChan <- response
	}()

	// Wait for initial response or timeout
	var response dify.ChatMessagesResponse
	select {
	case response = <-responseChan:
		break
	case err := <-errorChan:
		return err
	case <-ctx.Done():
		return fmt.Errorf("AI conversation timeout")
	}

	// Update conversion with Dify conversation ID
	conversion.DifyConversationID = response.ConversationID
	if err := conversion.UpdateConversion(tc.DB); err != nil {
		logger.Warning("Failed to update conversion with conversation ID: %v", err)
	}

	statusChan <- "AI conversation started, processing responses..."

	// Process AI responses with round limits
	return tc.processAIConversation(ctx, conversion, toolsManager, response, statusChan, 0)
}

// updateConversionStatus updates conversion status and AI thoughts
func (tc *TemplateConversionController) updateConversionStatus(conversion *models.TemplateConversion, statusChan <-chan string) {
	for status := range statusChan {
		// Update status and log AI thoughts - use existing status mechanism
		conversion.Status = "in_progress"
		if err := conversion.UpdateConversion(tc.DB); err != nil {
			logger.Warning("Failed to update conversion status: %v", err)
		}
		logger.Info("Conversion %s: %s", conversion.ID.String(), status)
	}
}

// processAIConversation handles the complete AI conversation with timeout and round limits
func (tc *TemplateConversionController) processAIConversation(ctx context.Context, conversion *models.TemplateConversion, toolsManager *template_tools.TemplateToolsManager, initialResponse dify.ChatMessagesResponse, statusChan chan<- string, round int) error {
	// Check round limit
	if round >= MAX_AI_CONVERSATION_ROUNDS {
		return fmt.Errorf("maximum conversation rounds (%d) exceeded", MAX_AI_CONVERSATION_ROUNDS)
	}

	// Check timeout
	select {
	case <-ctx.Done():
		return fmt.Errorf("conversation timeout")
	default:
	}

	// Process the response
	toolResult, hasMoreWork, err := tc.processAIResponseWithTools(toolsManager, initialResponse, statusChan)
	if err != nil {
		return err
	}

	// If no more work or task completed, finish
	if !hasMoreWork {
		statusChan <- "AI conversion completed successfully"
		return nil
	}

	// Continue conversation if needed
	return tc.continueAIConversationWithTimeout(ctx, conversion, toolsManager, toolResult, statusChan, round+1)
}

// processAIResponseWithTools processes AI response and executes tools
func (tc *TemplateConversionController) processAIResponseWithTools(toolsManager *template_tools.TemplateToolsManager, response dify.ChatMessagesResponse, statusChan chan<- string) (*template_tools.ToolResponse, bool, error) {
	// Parse AI response for tool calls
	var aiResponse struct {
		Thought string `json:"thought"`
		Action  struct {
			ToolName   string                 `json:"tool_name"`
			Parameters map[string]interface{} `json:"parameters"`
		} `json:"action"`
	}

	// Update status with AI thoughts
	if response.Answer != "" {
		statusChan <- fmt.Sprintf("AI thinking: %s", response.Answer)
	}

	// Try to parse as JSON
	if err := json.Unmarshal([]byte(response.Answer), &aiResponse); err != nil {
		// If not JSON, treat as regular response and continue
		statusChan <- fmt.Sprintf("AI response: %s", response.Answer)
		return nil, false, nil
	}

	// Update status with AI thoughts
	if aiResponse.Thought != "" {
		statusChan <- fmt.Sprintf("AI思考: %s", aiResponse.Thought)
	}

	// Execute tool if specified
	if aiResponse.Action.ToolName != "" {
		statusChan <- fmt.Sprintf("执行工具: %s", aiResponse.Action.ToolName)

		toolResponse := toolsManager.ExecuteTool(aiResponse.Action.ToolName, aiResponse.Action.Parameters)

		// Log tool execution
		logger.Info("Tool executed: %s, Success: %v, Message: %s",
			aiResponse.Action.ToolName, toolResponse.Success, toolResponse.Message)

		// Update status with tool result
		statusChan <- fmt.Sprintf("工具执行结果: %s - %s", aiResponse.Action.ToolName, toolResponse.Message)

		// If tool execution failed, return error
		if !toolResponse.Success {
			return nil, false, fmt.Errorf("tool execution failed: %s", toolResponse.Message)
		}

		// If this was the complete_task tool, we're done
		if aiResponse.Action.ToolName == "complete_task" {
			return &toolResponse, false, nil
		}

		// Continue conversation with tool result
		return &toolResponse, true, nil
	}

	return nil, false, nil
}

// continueAIConversationWithTimeout continues the conversation with timeout control
func (tc *TemplateConversionController) continueAIConversationWithTimeout(ctx context.Context, conversion *models.TemplateConversion, toolsManager *template_tools.TemplateToolsManager, toolResponse *template_tools.ToolResponse, statusChan chan<- string, round int) error {
	// Create timeout context for this round
	roundCtx, cancel := context.WithTimeout(ctx, AI_RESPONSE_TIMEOUT)
	defer cancel()

	// Initialize Dify client
	difyClient, err := dify.GetDifyClient()
	if err != nil {
		return fmt.Errorf("failed to initialize Dify client: %w", err)
	}

	// Prepare follow-up query with tool result
	query := fmt.Sprintf("Tool execution result: Success=%v, Message=%s, Data=%s. Please continue with the next step in JSON format.",
		toolResponse.Success, toolResponse.Message, toolResponse.Data)

	statusChan <- fmt.Sprintf("第%d轮对话: 发送工具结果给AI", round)

	// Create channels for this round
	responseChan := make(chan dify.ChatMessagesResponse, 1)
	errorChan := make(chan error, 1)

	// Start API call in goroutine
	go func() {
		response, err := difyClient.ChatMessages(
			query,
			map[string]interface{}{},
			conversion.DifyConversationID,
			[]dify.ChatMessagesPayloadFile{},
			DIFY_APP_KEY,
		)

		if err != nil {
			errorChan <- fmt.Errorf("failed to continue AI conversation: %w", err)
			return
		}

		responseChan <- response
	}()

	// Wait for response with timeout
	select {
	case response := <-responseChan:
		// Process the response recursively
		return tc.processAIConversation(ctx, conversion, toolsManager, response, statusChan, round)
	case err := <-errorChan:
		return err
	case <-roundCtx.Done():
		return fmt.Errorf("AI response timeout for round %d", round)
	case <-ctx.Done():
		return fmt.Errorf("overall conversation timeout")
	}
}

// uploadResultFile uploads the result ZIP file to S3
func (tc *TemplateConversionController) uploadResultFile(conversion *models.TemplateConversion, resultZipPath string) error {
	// Calculate result file hash
	resultFile, err := os.Open(resultZipPath)
	if err != nil {
		return fmt.Errorf("failed to open result file: %w", err)
	}
	defer resultFile.Close()

	hash := sha256.New()
	if _, err := io.Copy(hash, resultFile); err != nil {
		return fmt.Errorf("failed to calculate result file hash: %w", err)
	}
	resultFileHash := hex.EncodeToString(hash.Sum(nil))

	// Reset file pointer
	if _, err := resultFile.Seek(0, 0); err != nil {
		return fmt.Errorf("failed to reset file pointer: %w", err)
	}

	// Get file info
	fileInfo, err := resultFile.Stat()
	if err != nil {
		return fmt.Errorf("failed to get file info: %w", err)
	}

	// Upload result file to S3
	putOpts := minio.PutObjectOptions{
		ContentType: "application/zip",
		UserMetadata: map[string]string{
			"hash":          resultFileHash,
			"conversion_id": conversion.ID.String(),
			"original_name": "result.zip",
		},
	}

	_, err = tc.S3.Client.PutObject(
		context.TODO(),
		tc.S3.BucketName,
		CONVERSION_FILES_PATH+"result_"+resultFileHash,
		resultFile,
		fileInfo.Size(),
		putOpts,
	)

	if err != nil {
		return fmt.Errorf("failed to upload result file: %w", err)
	}

	// Update conversion record with result file info
	conversion.ResultFileHash = resultFileHash
	conversion.ResultFileName = "result.zip"
	conversion.ResultFileSize = fileInfo.Size()

	return conversion.UpdateConversion(tc.DB)
}

// GetConversionStatus returns the real-time status of a conversion
func (tc *TemplateConversionController) GetConversionStatus(ctx *gin.Context) {
	currentUser := ctx.MustGet("currentUser").(models.User)

	conversionID, err := uuid.Parse(ctx.Param("id"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "Invalid conversion ID"})
		return
	}

	conversion, err := models.GetConversionByID(tc.DB, conversionID)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"message": "Conversion not found"})
		return
	}

	// Check if user owns this conversion
	if conversion.UserID != currentUser.ID {
		ctx.JSON(http.StatusForbidden, gin.H{"message": "Access denied"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"conversion": conversion,
		"status":     conversion.Status,
		"progress":   tc.getConversionProgress(conversion),
	})
}

// getConversionProgress calculates conversion progress based on status
func (tc *TemplateConversionController) getConversionProgress(conversion *models.TemplateConversion) map[string]interface{} {
	progress := map[string]interface{}{
		"percentage": 0,
		"stage":      "pending",
		"message":    "Waiting to start",
	}

	switch conversion.Status {
	case models.ConversionStatusPending:
		progress["percentage"] = 0
		progress["stage"] = "pending"
		progress["message"] = "Conversion task queued"
	case "in_progress":
		progress["percentage"] = 50
		progress["stage"] = "processing"
		progress["message"] = "AI is analyzing and converting files"
	case models.ConversionStatusCompleted:
		progress["percentage"] = 100
		progress["stage"] = "completed"
		progress["message"] = "Conversion completed successfully"
	case models.ConversionStatusFailed:
		progress["percentage"] = 0
		progress["stage"] = "failed"
		progress["message"] = "Conversion failed"
		if conversion.ErrorMessage != "" {
			progress["error"] = conversion.ErrorMessage
		}
	}

	return progress
}
