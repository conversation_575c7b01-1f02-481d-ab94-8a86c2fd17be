load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "dify",
    srcs = [
        "api.go",
        "api-autoComplete.go",
        "api-chat-messages.go",
        "api-conversations.go",
        "apps.go",
        "client.go",
        "console-datasets.go",
        "console-datasets-file-upload.go",
        "console-datasets-init.go",
        "console-workspaces.go",
        "const.go",
        "files.go",
        "fn.go",
        "fn-api.go",
        "fn-console.go",
        "login.go",
        "messages.go",
        "model.go",
        "passport.go",
        "tags.go",
    ],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/dify",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/internal/config",
        "//golangp/apps/arx_center/pkg/logger",
    ],
)
