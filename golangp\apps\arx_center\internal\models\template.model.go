package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Template represents a LaTeX template that can be used for conversion
type Template struct {
	ID        uuid.UUID      `gorm:"type:uuid;primary_key" json:"id"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime" json:"updatedAt"`
	CreatedAt time.Time      `gorm:"autoCreateTime" json:"createdAt"`
	DeletedAt gorm.DeletedAt `gorm:"index"`

	// Template basic information
	Name        string `gorm:"type:varchar(255);not null" json:"name"`
	Description string `gorm:"type:text" json:"description"`
	Category    string `gorm:"type:varchar(100)" json:"category"` // e.g., "conference", "journal", "thesis"

	// Template file information
	FileHash string `gorm:"type:varchar(64);not null;unique" json:"fileHash"` // SHA256 hash of the template ZIP file
	FileSize int64  `gorm:"type:bigint;not null" json:"fileSize"`
	FileName string `gorm:"type:varchar(255);not null" json:"fileName"`

	// Template metadata
	Version string `gorm:"type:varchar(50)" json:"version"`
	Author  string `gorm:"type:varchar(255)" json:"author"`
	License string `gorm:"type:varchar(100)" json:"license"`
	Tags    string `gorm:"type:text" json:"tags"` // JSON array of tags

	// Template status
	IsActive bool `gorm:"default:true" json:"isActive"`
	IsPublic bool `gorm:"default:false" json:"isPublic"`

	// Ownership
	OwnerID uuid.UUID `gorm:"type:uuid;not null" json:"ownerId"`
	Owner   *User     `gorm:"foreignKey:OwnerID" json:"owner"`

	// Usage statistics
	UsageCount int64 `gorm:"default:0" json:"usageCount"`
}

// CreateTemplatePayload represents the payload for creating a new template
type CreateTemplatePayload struct {
	Name        string `json:"name" form:"name" binding:"required"`
	Description string `json:"description" form:"description"`
	Category    string `json:"category" form:"category"`
	Version     string `json:"version" form:"version"`
	Author      string `json:"author" form:"author"`
	License     string `json:"license" form:"license"`
	Tags        string `json:"tags" form:"tags"`
	IsPublic    bool   `json:"isPublic" form:"isPublic"`
}

// UpdateTemplatePayload represents the payload for updating a template
type UpdateTemplatePayload struct {
	Name        *string `json:"name" form:"name"`
	Description *string `json:"description" form:"description"`
	Category    *string `json:"category" form:"category"`
	Version     *string `json:"version" form:"version"`
	Author      *string `json:"author" form:"author"`
	License     *string `json:"license" form:"license"`
	Tags        *string `json:"tags" form:"tags"`
	IsActive    *bool   `json:"isActive" form:"isActive"`
	IsPublic    *bool   `json:"isPublic" form:"isPublic"`
}

// TemplateResponse represents the response structure for template data
type TemplateResponse struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Category    string    `json:"category"`
	Version     string    `json:"version"`
	Author      string    `json:"author"`
	License     string    `json:"license"`
	Tags        string    `json:"tags"`
	IsActive    bool      `json:"isActive"`
	IsPublic    bool      `json:"isPublic"`
	UsageCount  int64     `json:"usageCount"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// GORM hooks and methods
func (t *Template) CreateTemplate(db *gorm.DB) error {
	return db.Create(t).Error
}

func (t *Template) UpdateTemplate(db *gorm.DB) error {
	return db.Save(t).Error
}

func (t *Template) DeleteTemplate(db *gorm.DB) error {
	return db.Delete(t).Error
}

// GetTemplateByID retrieves a template by its ID
func GetTemplateByID(db *gorm.DB, id uuid.UUID) (*Template, error) {
	var template Template
	err := db.Preload("Owner").First(&template, "id = ?", id).Error
	return &template, err
}

// GetTemplatesByOwner retrieves all templates owned by a specific user
func GetTemplatesByOwner(db *gorm.DB, ownerID uuid.UUID) ([]Template, error) {
	var templates []Template
	err := db.Where("owner_id = ?", ownerID).Find(&templates).Error
	return templates, err
}

// GetPublicTemplates retrieves all public templates
func GetPublicTemplates(db *gorm.DB) ([]Template, error) {
	var templates []Template
	err := db.Where("is_public = ? AND is_active = ?", true, true).Find(&templates).Error
	return templates, err
}

// IncrementUsageCount increments the usage count for a template
func (t *Template) IncrementUsageCount(db *gorm.DB) error {
	return db.Model(t).Update("usage_count", gorm.Expr("usage_count + ?", 1)).Error
}
