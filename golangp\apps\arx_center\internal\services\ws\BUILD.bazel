load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "ws",
    srcs = [
        "collaborative.edit.websocket.go",
        "rest.go",
        "room.go",
        "subscribers.go",
    ],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/services/ws",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/internal/config",
        "//golangp/apps/arx_center/internal/models",
        "//golangp/apps/arx_center/pkg/constants",
        "//golangp/apps/arx_center/pkg/utils",
        "//golangp/apps/arx_center/pkg/xminio",
        "@com_github_gin_gonic_gin//:gin",
        "@org_golang_x_net//websocket",
    ],
)
