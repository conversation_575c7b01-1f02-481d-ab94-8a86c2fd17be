package project_controller

import (
	"net/http"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/models"
	"github.com/gin-gonic/gin"
)

type UpdateAssetsPayload struct {
	Assets       []string `json:"assets" binding:"required"`
	DocumentSize int64    `json:"documentSize" binding:"required"`
}

func (pc *ProjectController) InternalUpdateAssets(ctx *gin.Context) {
	// Validate project ID
	project, err := pc.validateProjectId(ctx, ctx.Param("id"), 0)
	if err != nil {
		return
	}

	var payload UpdateAssetsPayload
	if err := ctx.ShouldBindJSON(&payload); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "Invalid request body"})
		return
	}

	// Load current project with existing assets
	if err := pc.DB.Preload("Assets").First(&project, project.ID).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to load project assets"})
		return
	}

	// Get existing assets from the database (ignore non-existent hashes)
	var newAssets []models.Asset
	if err := pc.DB.Where("hash IN ?", payload.Assets).Find(&newAssets).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to validate assets"})
		return
	}

	// Check if assets have changed
	assetsChanged := false
	if len(project.Assets) != len(newAssets) {
		assetsChanged = true
	} else {
		// Create maps for quick comparison
		currentHashMap := make(map[string]bool)
		for _, asset := range project.Assets {
			currentHashMap[asset.Hash] = true
		}

		newHashMap := make(map[string]bool)
		for _, asset := range newAssets {
			newHashMap[asset.Hash] = true
		}

		// Check if any hash is different
		for hash := range currentHashMap {
			if !newHashMap[hash] {
				assetsChanged = true
				break
			}
		}
		if !assetsChanged {
			for hash := range newHashMap {
				if !currentHashMap[hash] {
					assetsChanged = true
					break
				}
			}
		}
	}

	// Start transaction to ensure data consistency
	tx := pc.DB.Begin()
	if tx.Error != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to start transaction"})
		return
	}

	// Update assets only if they have changed
	if assetsChanged {
		// Clear existing project-asset associations
		if err := tx.Model(&project).Association("Assets").Clear(); err != nil {
			tx.Rollback()
			ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to clear existing asset associations"})
			return
		}

		// Add new asset associations
		if err := tx.Model(&project).Association("Assets").Append(newAssets); err != nil {
			tx.Rollback()
			ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to create asset associations"})
			return
		}
	}

	// Always update document size
	if err := tx.Model(&project).Update("document_size", payload.DocumentSize).Error; err != nil {
		tx.Rollback()
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to update document size"})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to commit changes"})
		return
	}

	updateMessage := "Document size updated successfully"
	if assetsChanged {
		updateMessage = "Assets and document size updated successfully"
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": updateMessage,
	})
}
