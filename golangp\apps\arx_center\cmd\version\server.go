package version

import (
	"fmt"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/constants"
	"github.com/spf13/cobra"
)

var (
	StartCmd = &cobra.Command{
		Use:     "version",
		Short:   "Get version info",
		Example: "ArxBackend version",
		PreRun: func(cmd *cobra.Command, args []string) {

		},
		RunE: func(cmd *cobra.Command, args []string) error {
			return run()
		},
	}
)

func run() error {
	fmt.Println(constants.Version)
	return nil
}
