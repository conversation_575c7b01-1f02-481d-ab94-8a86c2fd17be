package routes

import (
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/api/controllers"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/middleware"

	"github.com/gin-gonic/gin"
)

type ConversationsRouteController struct {
	ConversationsController controllers.ConversationsController
}

func NewConversationsRouteController(conversationsController controllers.ConversationsController) ConversationsRouteController {

	return ConversationsRouteController{conversationsController}
}

func (dc *ConversationsRouteController) ConversationsRoute(rg *gin.RouterGroup) {

	chatRouter := rg.Group("/chat")
	chatRouter.Use(middleware.DeserializeUser())
	chatRouter.GET("/access_token", dc.ConversationsController.GetConversationsAccessToken)
	chatRouter.GET("/app", dc.ConversationsController.GetAppList)
	chatRouter.POST("/chat-messages", dc.ConversationsController.ChatMessages)
	chatRouter.POST("/chat-messages/:task_id/stop", dc.ConversationsController.ChatMessagesStop)
	chatRouter.POST("/upload", dc.ConversationsController.HandleFileUploadForChat)
	chatRouter.POST("/auto-complete", dc.ConversationsController.AutoComplete)

	chatRouter.GET("/file/:file_id/preview", dc.ConversationsController.PreviewFile)
}
