load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "xminio",
    srcs = ["object-post.go"],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/xminio",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/internal/config",
        "@com_github_minio_minio_go_v7//:minio-go",
        "@com_github_minio_minio_go_v7//pkg/credentials",
    ],
)

go_test(
    name = "xminio_test",
    srcs = ["object-post_test.go"],
    embed = [":xminio"],
)
