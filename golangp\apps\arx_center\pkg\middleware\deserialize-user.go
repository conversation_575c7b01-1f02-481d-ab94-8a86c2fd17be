package middleware

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/config"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/models"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/initializers"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/utils"
	"github.com/golang-jwt/jwt/v5"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func DeserializeUser() gin.HandlerFunc {
	return deserializeUserWithCheck(nil)
}

// DeserializeUserWithCheck is similar to DeserializeUser but accepts a function
// to check if the authenticated user meets certain requirements (e.g., role, permissions)
func DeserializeUserWithCheck(checkFunc func(models.User) (bool, string)) gin.HandlerFunc {
	return deserializeUserWithCheck(checkFunc)
}

// deserializeUserWithCheck is the core authentication logic shared by both public functions
func deserializeUserWithCheck(checkFunc func(models.User) (bool, string)) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		configCopy := config.Config

		// Step 1: Try Access Token
		accessToken := extractAccessToken(ctx)
		user, tokenValid := tryAccessToken(accessToken, ctx)
		if tokenValid {
			if !performUserCheck(user, checkFunc, ctx) {
				return
			}
			ctx.Set("currentUser", user)
			ctx.Next()
			return
		}

		// Step 2: Try Refresh Token
		refreshTokenID, ok := extractRefreshToken(ctx)
		if !ok {
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"status": "fail", "message": "You are not logged in"})
			return
		}

		user, ok = tryRefreshToken(refreshTokenID, ctx)
		if !ok {
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"status": "fail", "message": "Refresh token expired or invalid"})
			return
		}

		// Check if user meets requirements after refresh token validation
		if !performUserCheck(user, checkFunc, ctx) {
			return
		}

		// Step 3: Issue new Access Token
		_, err := createAndSetNewAccessToken(user, configCopy, ctx)
		if err != nil {
			ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"status": "fail", "message": "Could not create new access token"})
			return
		}

		// Continue
		ctx.Set("currentUser", user)
		ctx.Next()
	}
}

// performUserCheck executes the user check function if provided
func performUserCheck(user models.User, checkFunc func(models.User) (bool, string), ctx *gin.Context) bool {
	if checkFunc != nil {
		if passes, errorMsg := checkFunc(user); !passes {
			ctx.AbortWithStatusJSON(http.StatusForbidden, gin.H{"status": "fail", "message": errorMsg})
			return false
		}
	}
	return true
}

// createAndSetNewAccessToken creates a new access token and sets it as a cookie
func createAndSetNewAccessToken(user models.User, configCopy config.ConfigType, ctx *gin.Context) (string, error) {
	claims := models.TokenClaims{
		UserID: user.ID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Minute * time.Duration(configCopy.RefreshTokenMaxAge))),
		},
	}

	newAccessToken, err := utils.CreateToken(claims)
	if err != nil {
		return "", err
	}

	domain := utils.MatchDomain(ctx.Request.Header.Get("Referer"), configCopy.Domain)
	ctx.SetCookie("access_token", newAccessToken, configCopy.AccessTokenMaxAge*60, "/", domain, false, true)

	return newAccessToken, nil
}

func extractAccessToken(ctx *gin.Context) string {
	cookieAccessToken, accessTokenErr := ctx.Cookie("access_token")
	authorizationHeader := ctx.Request.Header.Get("Authorization")
	fields := strings.Fields(authorizationHeader)

	if len(fields) != 0 && fields[0] == "Bearer" {
		return fields[1]
	} else if accessTokenErr == nil {
		return cookieAccessToken
	}

	return ""
}

func tryAccessToken(accessToken string, ctx *gin.Context) (models.User, bool) {
	var user models.User

	if accessToken == "" {
		return user, false
	}
	var claims models.TokenClaims
	err := utils.ValidateToken(accessToken, &claims)
	if err != nil {
		return user, false
	}

	result := initializers.DB.First(&user, "id = ?", fmt.Sprint(claims.UserID))
	if result.Error != nil {
		ctx.AbortWithStatusJSON(http.StatusForbidden, gin.H{"status": "fail", "message": "The user belonging to this token no longer exists"})
		return user, false
	}

	return user, true
}

func extractRefreshToken(ctx *gin.Context) (uuid.UUID, bool) {
	cookieRefreshToken, refreshTokenErr := ctx.Cookie("refresh_token")
	if refreshTokenErr != nil {
		return uuid.Nil, false
	}

	refreshTokenUUID, uuidErr := uuid.Parse(cookieRefreshToken)
	if uuidErr != nil {
		ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"status": "fail", "message": "Invalid refresh token format"})
		return uuid.Nil, false
	}

	return refreshTokenUUID, true
}

func tryRefreshToken(refreshTokenID uuid.UUID, ctx *gin.Context) (models.User, bool) {
	var user models.User
	var refreshToken models.RefreshToken

	result := initializers.DB.First(&refreshToken, "id = ?", refreshTokenID)
	if result.Error != nil || refreshToken.ExpiredAt.Before(time.Now()) {
		return user, false
	}

	result = initializers.DB.First(&user, "id = ?", refreshToken.UserID)
	if result.Error != nil {
		ctx.AbortWithStatusJSON(http.StatusForbidden, gin.H{"status": "fail", "message": "The user belonging to this token no longer exists"})
		return user, false
	}

	return user, true
}
