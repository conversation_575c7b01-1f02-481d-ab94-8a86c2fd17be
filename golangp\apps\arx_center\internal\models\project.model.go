package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Project struct {
	ID          uuid.UUID      `gorm:"type:uuid;primary_key" json:"id"`
	UpdatedAt   time.Time      `gorm:"autoUpdateTime" json:"updatedAt"`
	CreatedAt   time.Time      `gorm:"autoCreateTime" json:"createdAt"`
	DeletedAt   gorm.DeletedAt `gorm:"index"`
	ProjectName string         `gorm:"type:text;" json:"projectName"`

	OwnerID      uuid.UUID `gorm:"type:uuid;" json:"ownerId"`
	Owner        *User     `gorm:"foreignKey:OwnerID" json:"owner"`
	Members      []*User   `gorm:"many2many:project_members;" json:"members"`
	Assets       []*Asset  `gorm:"many2many:project_assets;" json:"assets"`
	DocumentSize int64     `json:"documentSize"`

	// Share
	CollaboratorShareToken string `json:"-"`
	ViewerShareToken       string `json:"-"`

	// Publish
	Published bool `gorm:"default:false" json:"published"`
}

type CreateProjectPayload struct {
	ProjectName string `json:"projectName" binding:"required"`
}

type UpdateProjectPayload struct {
	ProjectName *string `json:"projectName"`
	Published   *bool   `json:"published"`
}

func (p *Project) CreateProject(db *gorm.DB) error {
	return db.Create(p).Error
}

func (p *Project) UpdateProject(db *gorm.DB) error {
	return db.Save(p).Error
}
