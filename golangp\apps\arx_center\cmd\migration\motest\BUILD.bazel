load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "motest",
    srcs = [
        "initdb.go",
        "motest.go",
    ],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/cmd/migration/motest",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/internal/config",
        "//golangp/apps/arx_center/internal/models",
        "//golangp/apps/arx_center/pkg/constants",
        "//golangp/apps/arx_center/pkg/initializers",
        "//golangp/apps/arx_center/pkg/logger",
        "//golangp/apps/arx_center/pkg/utils",
        "@io_gorm_gorm//:gorm",
    ],
)
