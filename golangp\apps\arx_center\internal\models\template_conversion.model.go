package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ConversionStatus represents the status of a template conversion task
type ConversionStatus string

const (
	ConversionStatusPending    ConversionStatus = "pending"
	ConversionStatusProcessing ConversionStatus = "processing"
	ConversionStatusCompleted  ConversionStatus = "completed"
	ConversionStatusFailed     ConversionStatus = "failed"
	ConversionStatusCancelled  ConversionStatus = "cancelled"
)

// TemplateConversion represents a template conversion task
type TemplateConversion struct {
	ID        uuid.UUID      `gorm:"type:uuid;primary_key" json:"id"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime" json:"updatedAt"`
	CreatedAt time.Time      `gorm:"autoCreateTime" json:"createdAt"`
	DeletedAt gorm.DeletedAt `gorm:"index"`

	// Task information
	TaskName string           `gorm:"type:varchar(255);not null" json:"taskName"`
	Status   ConversionStatus `gorm:"type:varchar(50);not null;default:'pending'" json:"status"`

	// Source and target information
	SourceFileHash string `gorm:"type:varchar(64);not null" json:"sourceFileHash"` // Hash of source ZIP file
	SourceFileName string `gorm:"type:varchar(255);not null" json:"sourceFileName"`
	SourceFileSize int64  `gorm:"type:bigint;not null" json:"sourceFileSize"`

	TargetTemplateID uuid.UUID `gorm:"type:uuid;not null" json:"targetTemplateId"`
	TargetTemplate   *Template `gorm:"foreignKey:TargetTemplateID" json:"targetTemplate"`

	// Result information
	ResultFileHash string `gorm:"type:varchar(64)" json:"resultFileHash"` // Hash of result ZIP file
	ResultFileName string `gorm:"type:varchar(255)" json:"resultFileName"`
	ResultFileSize int64  `gorm:"type:bigint" json:"resultFileSize"`

	// AI processing information
	DifyConversationID string `gorm:"type:varchar(255)" json:"difyConversationId"` // Dify conversation ID for tracking
	DifyTaskID         string `gorm:"type:varchar(255)" json:"difyTaskId"`         // Dify task ID for tracking

	// Processing details
	ProcessingLog    string     `gorm:"type:text" json:"processingLog"`    // Detailed processing log
	ErrorMessage     string     `gorm:"type:text" json:"errorMessage"`     // Error message if failed
	StartedAt        *time.Time `json:"startedAt"`                         // When processing started
	CompletedAt      *time.Time `json:"completedAt"`                       // When processing completed
	ProcessingTimeMs int64      `gorm:"default:0" json:"processingTimeMs"` // Processing time in milliseconds

	// Ownership
	UserID uuid.UUID `gorm:"type:uuid;not null" json:"userId"`
	User   *User     `gorm:"foreignKey:UserID" json:"user"`

	// Metadata
	Metadata string `gorm:"type:text" json:"metadata"` // JSON metadata for additional information
}

// CreateConversionPayload represents the payload for creating a new conversion task
type CreateConversionPayload struct {
	TaskName         string    `form:"taskName" binding:"required"`
	TargetTemplateID uuid.UUID `form:"targetTemplateId" binding:"required"`
}

// UpdateConversionPayload represents the payload for updating a conversion task
type UpdateConversionPayload struct {
	Status             *ConversionStatus `json:"status" form:"status"`
	ProcessingLog      *string           `json:"processingLog" form:"processingLog"`
	ErrorMessage       *string           `json:"errorMessage" form:"errorMessage"`
	ResultFileHash     *string           `json:"resultFileHash" form:"resultFileHash"`
	ResultFileName     *string           `json:"resultFileName" form:"resultFileName"`
	ResultFileSize     *int64            `json:"resultFileSize" form:"resultFileSize"`
	DifyConversationID *string           `json:"difyConversationId" form:"difyConversationId"`
	DifyTaskID         *string           `json:"difyTaskId" form:"difyTaskId"`
	Metadata           *string           `json:"metadata" form:"metadata"`
}

// ConversionResponse represents the response structure for conversion data
type ConversionResponse struct {
	ID               uuid.UUID        `json:"id"`
	TaskName         string           `json:"taskName"`
	Status           ConversionStatus `json:"status"`
	SourceFileName   string           `json:"sourceFileName"`
	SourceFileSize   int64            `json:"sourceFileSize"`
	TargetTemplate   TemplateResponse `json:"targetTemplate"`
	ResultFileName   string           `json:"resultFileName"`
	ResultFileSize   int64            `json:"resultFileSize"`
	ProcessingLog    string           `json:"processingLog"`
	ErrorMessage     string           `json:"errorMessage"`
	StartedAt        *time.Time       `json:"startedAt"`
	CompletedAt      *time.Time       `json:"completedAt"`
	ProcessingTimeMs int64            `json:"processingTimeMs"`
	CreatedAt        time.Time        `json:"createdAt"`
	UpdatedAt        time.Time        `json:"updatedAt"`
}

// GORM hooks and methods
func (tc *TemplateConversion) CreateConversion(db *gorm.DB) error {
	return db.Create(tc).Error
}

func (tc *TemplateConversion) UpdateConversion(db *gorm.DB) error {
	return db.Save(tc).Error
}

func (tc *TemplateConversion) DeleteConversion(db *gorm.DB) error {
	return db.Delete(tc).Error
}

// GetConversionByID retrieves a conversion task by its ID
func GetConversionByID(db *gorm.DB, id uuid.UUID) (*TemplateConversion, error) {
	var conversion TemplateConversion
	err := db.Preload("TargetTemplate").Preload("User").First(&conversion, "id = ?", id).Error
	return &conversion, err
}

// GetConversionsByUser retrieves all conversion tasks for a specific user
func GetConversionsByUser(db *gorm.DB, userID uuid.UUID) ([]TemplateConversion, error) {
	var conversions []TemplateConversion
	err := db.Preload("TargetTemplate").Where("user_id = ?", userID).Order("created_at DESC").Find(&conversions).Error
	return conversions, err
}

// GetActiveConversions retrieves all active (processing) conversion tasks
func GetActiveConversions(db *gorm.DB) ([]TemplateConversion, error) {
	var conversions []TemplateConversion
	err := db.Where("status = ?", ConversionStatusProcessing).Find(&conversions).Error
	return conversions, err
}

// MarkAsStarted marks the conversion as started and records the start time
func (tc *TemplateConversion) MarkAsStarted(db *gorm.DB) error {
	now := time.Now()
	tc.Status = ConversionStatusProcessing
	tc.StartedAt = &now
	return tc.UpdateConversion(db)
}

// MarkAsCompleted marks the conversion as completed and records the completion time
func (tc *TemplateConversion) MarkAsCompleted(db *gorm.DB) error {
	now := time.Now()
	tc.Status = ConversionStatusCompleted
	tc.CompletedAt = &now
	if tc.StartedAt != nil {
		tc.ProcessingTimeMs = now.Sub(*tc.StartedAt).Milliseconds()
	}
	return tc.UpdateConversion(db)
}

// MarkAsFailed marks the conversion as failed with an error message
func (tc *TemplateConversion) MarkAsFailed(db *gorm.DB, errorMessage string) error {
	now := time.Now()
	tc.Status = ConversionStatusFailed
	tc.ErrorMessage = errorMessage
	tc.CompletedAt = &now
	if tc.StartedAt != nil {
		tc.ProcessingTimeMs = now.Sub(*tc.StartedAt).Milliseconds()
	}
	return tc.UpdateConversion(db)
}
