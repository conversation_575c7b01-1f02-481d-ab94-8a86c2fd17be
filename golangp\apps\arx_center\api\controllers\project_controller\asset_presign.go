package project_controller

import (
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/config"
	"github.com/gin-gonic/gin"
	"github.com/minio/minio-go/v7"
)

type PresignDownloadRequest struct {
	Hashes []string `json:"hashes" binding:"required"`
}

// PresignedURLResponse defines the response structure for each file
type PresignedURLResponse struct {
	Hash  string `json:"hash"`
	URL   string `json:"url,omitempty"`
	Error string `json:"error,omitempty"`
}

func (pc *ProjectController) GeneratePresignedDownloadURLs(ctx *gin.Context) {
	var req PresignDownloadRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "Invalid request body"})
		return
	}

	if len(req.<PERSON>) == 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "At least one hash is required"})
		return
	}

	// Initialize results slice and synchronization primitives
	results := make([]PresignedURLResponse, len(req.Hashes))
	urlExpiry := 24 * time.Hour // URL valid for 24 hours, adjust as needed
	var wg sync.WaitGroup
	resultChan := make(chan struct {
		index  int
		result PresignedURLResponse
	}, len(req.Hashes))

	// Process each hash concurrently
	for i, hash := range req.Hashes {
		wg.Add(1)
		go func(index int, hash string) {
			defer wg.Done()
			result := PresignedURLResponse{Hash: hash}
			// Check if file exists in S3
			s3Path := PROJECT_ASSETS_PATH + hash // Assuming hash is used as the S3 object key, adjust if needed
			_, err := pc.S3.Client.StatObject(ctx,
				pc.S3.BucketName,
				s3Path,
				minio.StatObjectOptions{},
			)
			if err != nil {
				result.Error = "File not found in S3"
				resultChan <- struct {
					index  int
					result PresignedURLResponse
				}{index, result}
				return
			}

			// Generate presigned URL
			objectUrl, err := pc.S3.Client.PresignedGetObject(
				ctx,
				pc.S3.BucketName,
				s3Path,
				urlExpiry,
				nil, // Query parameters (optional)
			)
			if err != nil {
				result.Error = "Failed to generate presigned URL"
				resultChan <- struct {
					index  int
					result PresignedURLResponse
				}{index, result}
				return
			}
			fmt.Println(objectUrl.String())

			if config.Config.OSSProxy {
				result.URL = "/api/v1/oss-service" + objectUrl.Path + "?" + objectUrl.RawQuery
			} else {
				result.URL = objectUrl.String()
			}

			resultChan <- struct {
				index  int
				result PresignedURLResponse
			}{index, result}
		}(i, hash)
	}

	// Close result channel after all goroutines complete
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// Collect results
	for res := range resultChan {
		results[res.index] = res.result
	}

	ctx.JSON(http.StatusOK, gin.H{
		"urls": results,
	})
}
