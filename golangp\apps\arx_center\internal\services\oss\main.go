package oss

import (
	"context"
	"log"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/config"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

const (
	PROJECT_ASSETS_PATH  = "project-assets/"
	PROJECT_PREVIEW_PATH = "project-preview/"
	MAX_FILE_SIZE_MB     = 20
)

type OssService struct {
	Client minio.Client
	Bucket string
}

func NewOssService(config *config.ConfigType) (*OssService, error) {
	client, err := minio.New(config.OSSHost, &minio.Options{
		Creds:  credentials.NewStaticV4(config.OSSAccessKey, config.OSSSecretKey, ""),
		Secure: config.OSSSecure,
	})

	if err != nil {
		return nil, err
	}

	found, err := client.BucketExists(context.Background(), config.OSSBucket)
	if err != nil {
		log.Fatalln(err)
	}

	if !found {
		err = client.MakeBucket(context.Background(), config.OSSBucket, minio.MakeBucketOptions{})
		if err != nil {
			log.Fatalln(err)
		}
		log.Printf("Created bucket %s\n", config.OSSBucket)
	}

	return &OssService{
		Client: *client,
		Bucket: config.OSSBucket,
	}, nil
}
