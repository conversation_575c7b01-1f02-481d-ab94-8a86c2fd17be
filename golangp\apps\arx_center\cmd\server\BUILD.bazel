load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "server",
    srcs = ["server.go"],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/cmd/server",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/api/controllers",
        "//golangp/apps/arx_center/api/controllers/project_controller",
        "//golangp/apps/arx_center/api/routes",
        "//golangp/apps/arx_center/cmd/migration/motest",
        "//golangp/apps/arx_center/internal/config",
        "//golangp/apps/arx_center/internal/services/oss",
        "//golangp/apps/arx_center/pkg/initializers",
        "//golangp/apps/arx_center/pkg/middleware",
        "//golangp/apps/arx_center/pkg/proxy",
        "@com_github_gin_contrib_cors//:cors",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_spf13_cobra//:cobra",
    ],
)
