package controllers

import (
	"net/http"
	"strings"
	"time"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/config"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/models"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/constants"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/thanhpk/randstr"
	"gorm.io/gorm"
)

type AuthController struct {
	DB *gorm.DB
}

func NewAuthController(DB *gorm.DB) AuthController {
	return AuthController{DB}
}

// SignUpUser SignUp User
func (ac *AuthController) SignUpUser(ctx *gin.Context) {
	var payload *models.SignUpInput

	if err := ctx.ShouldBindJSON(&payload); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": err.Error()})
		return
	}

	if payload.Password != payload.PasswordConfirm {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": "Passwords do not match"})
		return
	}

	hashedPassword, err := utils.HashPassword(payload.Password)
	if err != nil {
		ctx.JSON(http.StatusBadGateway, gin.H{"status": "error", "message": err.Error()})
		return
	}

	now := time.Now()
	newUser := models.User{
		ID:        uuid.New(),
		Name:      payload.Name,
		Email:     strings.ToLower(payload.Email),
		Password:  hashedPassword,
		Role:      constants.AppRoleUser,
		Verified:  false,
		Photo:     "test",
		Provider:  "local",
		CreatedAt: now,
		UpdatedAt: now,
	}

	result := ac.DB.Create(&newUser)

	if result.Error != nil && strings.Contains(result.Error.Error(), "duplicate key") {
		ctx.JSON(http.StatusConflict, gin.H{"status": "fail", "message": "User with that username or email already exists, " +
			"try use forget password to reset it."})
		return
	} else if result.Error != nil {
		ctx.Error(result.Error)
		ctx.JSON(http.StatusBadGateway, gin.H{"status": "error", "message": "Internal server error, please try again later."})
		return
	}

	// Generate Verification Code
	code := randstr.String(6)
	verificationCode := utils.Encode(code)
	newUser.VerificationCode = verificationCode
	ac.DB.Save(newUser)

	configCopy := config.Config

	var firstName = newUser.Name

	if strings.Contains(firstName, " ") {
		firstName = strings.Split(firstName, " ")[1]
	}

	// ? Send Email
	emailData := utils.AccountEmailData{
		URL:              configCopy.ClientOrigin + "/verify-email?code=" + code,
		VerificationCode: code,
		FirstName:        firstName,
		Subject:          "Verify your arXtect email address",
	}

	go utils.SendAccountEmail(&newUser, &emailData, "verificationCode.html")

	message := "We sent an email with a verification code to " + newUser.Email
	ctx.JSON(http.StatusCreated, gin.H{"status": "success", "message": message})

}

type TokenClaims struct {
	UserID uuid.UUID `json:"userId"`
	jwt.RegisteredClaims
}

func (ac *AuthController) SignInUser(ctx *gin.Context) {
	var payload *models.SignInInput

	if err := ctx.ShouldBindJSON(&payload); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": err.Error()})
		return
	}

	var user models.User
	result := ac.DB.First(&user, "email = ?", strings.ToLower(payload.Email))
	if result.Error != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": "Invalid email or Password"})
		return
	}

	if !user.Verified {
		ctx.JSON(http.StatusForbidden, gin.H{"status": "fail", "message": "Please verify your email"})
		return
	}

	if err := utils.VerifyPassword(user.Password, payload.Password); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": "Invalid email or Password"})
		return
	}

	configCopy := config.Config

	// Generate Tokens
	tokenClaim := models.TokenClaims{
		UserID: user.ID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Minute * time.Duration(configCopy.AccessTokenMaxAge))),
		},
	}

	accessToken, err := utils.CreateToken(tokenClaim)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": err.Error()})
		return
	}

	refreshToken := models.RefreshToken{
		ID:        uuid.New(),
		UserID:    user.ID,
		ExpiredAt: time.Now().Add(time.Minute * time.Duration(configCopy.RefreshTokenMaxAge)),
	}

	if err := ac.DB.Create(&refreshToken).Error; err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": "Could not create refresh token"})
		return
	}

	domain := utils.MatchDomain(ctx.Request.Header.Get("Referer"), configCopy.Domain)
	ctx.SetCookie("access_token", accessToken, configCopy.AccessTokenMaxAge*60, "/", domain, false, true)
	ctx.SetCookie("refresh_token", refreshToken.ID.String(), configCopy.RefreshTokenMaxAge*60, "/", domain, false, true)
	ctx.JSON(http.StatusOK, gin.H{"status": "success", "access_token": accessToken})
}

func (ac *AuthController) LogoutUser(ctx *gin.Context) {
	configCopy := config.Config
	domain := utils.MatchDomain(ctx.Request.Header.Get("Referer"), configCopy.Domain)
	ctx.SetCookie("access_token", "", -1, "/", domain, false, true)
	ctx.SetCookie("refresh_token", "", -1, "/", domain, false, true)
	ctx.JSON(http.StatusOK, gin.H{"status": "success"})
}

// VerifyEmail [...] Verify Email
func (ac *AuthController) VerifyEmail(ctx *gin.Context) {
	var payload *models.CodeInput
	if err := ctx.ShouldBindJSON(&payload); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": err.Error()})
		return
	}

	code := payload.Code
	verificationCode := utils.Encode(code)

	var updatedUser models.User
	result := ac.DB.First(&updatedUser, "verification_code = ?", verificationCode)
	if result.Error != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": "Invalid verification code or user doesn't exists"})
		return
	}

	if updatedUser.Verified {
		ctx.JSON(http.StatusConflict, gin.H{"status": "fail", "message": "User already verified"})
		return
	}

	updatedUser.VerificationCode = ""
	updatedUser.Verified = true
	ac.DB.Save(&updatedUser)

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "message": "Email verified successfully"})
}

func (ac *AuthController) ForgotPassword(ctx *gin.Context) {
	var payload *models.ForgotPasswordInput

	if err := ctx.ShouldBindJSON(&payload); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": err.Error()})
		return
	}

	message := "You will receive a reset email if user with that email exist"

	var user models.User
	result := ac.DB.First(&user, "email = ?", strings.ToLower(payload.Email))
	if result.Error != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": "Invalid email or Password"})
		return
	}

	configCopy := config.Config

	// Generate Verification Code
	resetToken := randstr.String(6)

	passwordResetToken := utils.Encode(resetToken)
	user.PasswordResetToken = passwordResetToken
	user.PasswordResetAt = time.Now().Add(time.Minute * 15)
	ac.DB.Save(&user)

	var firstName = user.Name

	if strings.Contains(firstName, " ") {
		firstName = strings.Split(firstName, " ")[1]
	}

	// ? Send Email
	emailData := utils.AccountEmailData{
		URL:              configCopy.ClientOrigin + "/reset-password?code=" + resetToken,
		VerificationCode: resetToken,
		FirstName:        firstName,
		Subject:          "Your password reset token (valid for 10min)",
	}

	go utils.SendAccountEmail(&user, &emailData, "resetPassword.html")

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "message": message})
}

func (ac *AuthController) ResetPassword(ctx *gin.Context) {
	var payload *models.ResetPasswordInput

	configCopy := config.Config

	if err := ctx.ShouldBindJSON(&payload); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": err.Error()})
		return
	}

	if payload.Password != payload.PasswordConfirm {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": "Passwords do not match"})
		return
	}

	hashedPassword, _ := utils.HashPassword(payload.Password)
	passwordResetToken := utils.Encode(payload.ResetToken)

	var updatedUser models.User
	result := ac.DB.First(&updatedUser, "password_reset_token = ? AND password_reset_at > ?", passwordResetToken, time.Now())
	if result.Error != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": "The reset token is invalid or has expired"})
		return
	}

	updatedUser.Password = hashedPassword
	updatedUser.Verified = true
	updatedUser.PasswordResetToken = ""

	ac.DB.Save(&updatedUser)
	domain := utils.MatchDomain(ctx.Request.Header.Get("Referer"), configCopy.Domain)
	ctx.SetCookie("token", "", -1, "/", domain, false, true)
	ctx.JSON(http.StatusOK, gin.H{"status": "success", "message": "Password data updated successfully"})
}
