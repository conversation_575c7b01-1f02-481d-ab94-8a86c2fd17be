package routes

import (
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/api/controllers/project_controller"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/middleware"

	"github.com/gin-gonic/gin"
)

type ProjectRouteController struct {
	projectController project_controller.ProjectController
}

func NewProjectRouteController(projectController project_controller.ProjectController) ProjectRouteController {
	return ProjectRouteController{projectController}
}

func (prc *ProjectRouteController) ProjectkRoute(rg *gin.RouterGroup) {
	router := rg.Group("projects").Use(middleware.DeserializeUser())
	router.GET("", prc.projectController.GetProjects)
	router.POST("", prc.projectController.CreateProject)
	router.GET("/:id", prc.projectController.GetProjectViaID)
	router.PATCH("/:id", prc.projectController.UpdateProject)
	router.DELETE("/:id", prc.projectController.DeleteProject)

	// project assets
	router.POST("/:id/assets/precheck-batch", prc.projectController.PreCheckUploadBatch)
	router.POST("/:id/assets/batch", prc.projectController.UploadAssetBatch)
	router.POST("/:id/assets/presigned-urls", prc.projectController.GeneratePresignedDownloadURLs)

	// project preview
	router.GET("/:id/preview", prc.projectController.GetPreview)
	router.POST("/:id/preview", prc.projectController.UploadPreview)

	// project share
	router.POST("/:id/share-tokens", prc.projectController.CreateShareToken)
	router.GET("/:id/share-tokens", prc.projectController.GetPublicTokens)
	router.GET("/share-tokens/:token", prc.projectController.GetTokenInfo)
	router.GET("/:id/members", prc.projectController.GetProjectMembers)
	router.POST("/:id/members", prc.projectController.AcceptProjectInvitation)
	router.DELETE("/:id/members/:userId", prc.projectController.RemoveMembers)
	router.GET("/:id/access", prc.projectController.GetProjectAccess)

	router = rg.Group("projects").Use(middleware.APIAuth())
	router.PATCH("/:id/assets", prc.projectController.InternalUpdateAssets)

	router = rg.Group("published-projects")
	router.GET("", prc.projectController.GetPublishedProjects)
}
