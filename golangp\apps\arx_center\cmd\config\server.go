package config

import (
	"fmt"
	"log"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/config"

	"github.com/spf13/cobra"
)

var (
	configYml string
	StartCmd  = &cobra.Command{
		Use:     "config",
		Short:   "Get Application config info",
		Example: "ArxBackend config -c config/settings-dev.yml",
		Run: func(cmd *cobra.Command, args []string) {
			run()
		},
	}
)

func init() {
	StartCmd.PersistentFlags().StringVarP(&configYml, "config", "c", "config/settings-dev.yml", "Start server with provided configuration file")
}

func run() {
	log.Println("🚗 Load configuration file ...")
	err := config.LoadConfig(configYml)
	if err != nil {
		log.Println("🚀 Load failed", err)
		return
	}
	fmt.Println("🚗 Load success.....", config.Config.Mode)
}
