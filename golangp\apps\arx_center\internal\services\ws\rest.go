package ws

import (
	"io"
	"io/ioutil"
	"net/http"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/config"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/utils"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/xminio"
)

func HandleGetStaticResource(filename string) func(http.ResponseWriter, *http.Request) {

	return func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" {
			w.WriteHeader(415)
			return
		}
		b, err := ioutil.ReadFile(filename)
		if err != nil {
			utils.WriteJSON(w, 400, err.Error())
			return
		}
		w.WriteHeader(200)
		io.WriteString(w, string(b))
	}
}
func HandleCreateRoom(fileId string) (string, string, error) {

	existence, errByMinio := xminio.NewS3Manager(config.Config.OSSBucket, config.Config.OSSAccessKey, config.Config.OSSSecretKey, config.Config.OSSHost).CheckFileExistence(fileId)
	if !existence || errByMinio != nil {
		return "", "", errByMinio
	}

	room, err := roomService.NewRoom(fileId)
	if err != nil {
		return "", "", err
	}

	Invitation := utils.RoomIdCreate(6)

	return room.ID, Invitation, nil
}
