package ziputils

import (
	"archive/zip"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

// createTestZip creates a test ZIP file with the given structure
func createTestZip(t *testing.T, files map[string]string) string {
	// Create temporary ZIP file
	tempFile, err := os.CreateTemp("", "test_*.zip")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer tempFile.Close()

	// Create ZIP writer
	zipWriter := zip.NewWriter(tempFile)
	defer zipWriter.Close()

	// Add files to ZIP
	for filename, content := range files {
		writer, err := zipWriter.Create(filename)
		if err != nil {
			t.Fatalf("Failed to create file in ZIP: %v", err)
		}
		_, err = writer.Write([]byte(content))
		if err != nil {
			t.Fatalf("Failed to write file content: %v", err)
		}
	}

	return tempFile.Name()
}

func TestExtractZip_SingleRootFolder(t *testing.T) {
	// Create test ZIP with single root folder
	files := map[string]string{
		"CVPR 2022/main.tex":           "\\documentclass{article}",
		"CVPR 2022/sections/intro.tex": "\\section{Introduction}",
		"CVPR 2022/figures/fig1.png":   "fake image data",
		"CVPR 2022/README.md":          "# CVPR Template",
	}

	zipPath := createTestZip(t, files)
	defer os.Remove(zipPath)

	// Create target directory
	targetDir, err := os.MkdirTemp("", "extract_test_")
	if err != nil {
		t.Fatalf("Failed to create target dir: %v", err)
	}
	defer os.RemoveAll(targetDir)

	// Extract ZIP
	err = ExtractZip(zipPath, targetDir)
	if err != nil {
		t.Fatalf("ExtractZip failed: %v", err)
	}

	// Verify files were extracted correctly (root folder should be stripped)
	expectedFiles := []string{
		"main.tex",
		"sections/intro.tex",
		"figures/fig1.png",
		"README.md",
	}

	for _, expectedFile := range expectedFiles {
		fullPath := filepath.Join(targetDir, expectedFile)
		if _, err := os.Stat(fullPath); os.IsNotExist(err) {
			t.Errorf("Expected file not found: %s", expectedFile)
		}
	}

	// Verify root folder was stripped
	rootFolderPath := filepath.Join(targetDir, "CVPR 2022")
	if _, err := os.Stat(rootFolderPath); !os.IsNotExist(err) {
		t.Errorf("Root folder should have been stripped but still exists: %s", rootFolderPath)
	}
}

func TestExtractZip_MultipleRootItems(t *testing.T) {
	// Create test ZIP with multiple items at root level
	files := map[string]string{
		"main.tex":           "\\documentclass{article}",
		"sections/intro.tex": "\\section{Introduction}",
		"figures/fig1.png":   "fake image data",
		"README.md":          "# Template",
	}

	zipPath := createTestZip(t, files)
	defer os.Remove(zipPath)

	// Create target directory
	targetDir, err := os.MkdirTemp("", "extract_test_")
	if err != nil {
		t.Fatalf("Failed to create target dir: %v", err)
	}
	defer os.RemoveAll(targetDir)

	// Extract ZIP
	err = ExtractZip(zipPath, targetDir)
	if err != nil {
		t.Fatalf("ExtractZip failed: %v", err)
	}

	// Verify all files were extracted as-is
	expectedFiles := []string{
		"main.tex",
		"sections/intro.tex",
		"figures/fig1.png",
		"README.md",
	}

	for _, expectedFile := range expectedFiles {
		fullPath := filepath.Join(targetDir, expectedFile)
		if _, err := os.Stat(fullPath); os.IsNotExist(err) {
			t.Errorf("Expected file not found: %s", expectedFile)
		}
	}
}

func TestExtractZip_WithMacOSFiles(t *testing.T) {
	// Create test ZIP with __MACOSX files that should be skipped
	files := map[string]string{
		"template/main.tex":                "\\documentclass{article}",
		"__MACOSX/template/._main.tex":     "mac metadata",
		"__MACOSX/._template":              "mac metadata",
		"template/sections/intro.tex":      "\\section{Introduction}",
		"__MACOSX/template/sections/._intro.tex": "mac metadata",
	}

	zipPath := createTestZip(t, files)
	defer os.Remove(zipPath)

	// Create target directory
	targetDir, err := os.MkdirTemp("", "extract_test_")
	if err != nil {
		t.Fatalf("Failed to create target dir: %v", err)
	}
	defer os.RemoveAll(targetDir)

	// Extract ZIP
	err = ExtractZip(zipPath, targetDir)
	if err != nil {
		t.Fatalf("ExtractZip failed: %v", err)
	}

	// Verify only valid files were extracted
	expectedFiles := []string{
		"main.tex",
		"sections/intro.tex",
	}

	for _, expectedFile := range expectedFiles {
		fullPath := filepath.Join(targetDir, expectedFile)
		if _, err := os.Stat(fullPath); os.IsNotExist(err) {
			t.Errorf("Expected file not found: %s", expectedFile)
		}
	}

	// Verify __MACOSX files were not extracted
	err = filepath.Walk(targetDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if strings.Contains(path, "__MACOSX") {
			t.Errorf("__MACOSX file should not have been extracted: %s", path)
		}
		return nil
	})
	if err != nil {
		t.Fatalf("Failed to walk target directory: %v", err)
	}
}

func TestExtractZip_UnsafePaths(t *testing.T) {
	// Create test ZIP with unsafe paths
	files := map[string]string{
		"../../../etc/passwd": "malicious content",
		"normal.tex":          "\\documentclass{article}",
	}

	zipPath := createTestZip(t, files)
	defer os.Remove(zipPath)

	// Create target directory
	targetDir, err := os.MkdirTemp("", "extract_test_")
	if err != nil {
		t.Fatalf("Failed to create target dir: %v", err)
	}
	defer os.RemoveAll(targetDir)

	// Extract ZIP - should fail due to unsafe path
	err = ExtractZip(zipPath, targetDir)
	if err == nil {
		t.Fatalf("ExtractZip should have failed due to unsafe path")
	}

	if !strings.Contains(err.Error(), "unsafe file path") {
		t.Errorf("Expected 'unsafe file path' error, got: %v", err)
	}
}

func TestValidateZipFile(t *testing.T) {
	// Create valid ZIP
	files := map[string]string{
		"main.tex": "\\documentclass{article}",
	}

	zipPath := createTestZip(t, files)
	defer os.Remove(zipPath)

	// Should pass validation
	err := ValidateZipFile(zipPath)
	if err != nil {
		t.Errorf("ValidateZipFile failed for valid ZIP: %v", err)
	}
}

func TestGetZipInfo(t *testing.T) {
	// Create test ZIP
	files := map[string]string{
		"main.tex":           "\\documentclass{article}",
		"sections/intro.tex": "\\section{Introduction}",
		"figures/fig1.png":   "fake image data",
		"README.md":          "# Template",
	}

	zipPath := createTestZip(t, files)
	defer os.Remove(zipPath)

	// Get ZIP info
	info, err := GetZipInfo(zipPath)
	if err != nil {
		t.Fatalf("GetZipInfo failed: %v", err)
	}

	// Verify info
	if info.TotalFiles != 4 {
		t.Errorf("Expected 4 files, got %d", info.TotalFiles)
	}

	if !info.HasTexFiles {
		t.Errorf("Expected HasTexFiles to be true")
	}

	if len(info.TexFiles) != 2 {
		t.Errorf("Expected 2 .tex files, got %d", len(info.TexFiles))
	}

	expectedTexFiles := []string{"main.tex", "sections/intro.tex"}
	for _, expected := range expectedTexFiles {
		found := false
		for _, actual := range info.TexFiles {
			if actual == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected .tex file not found: %s", expected)
		}
	}
}

func TestCleanupTempDir(t *testing.T) {
	// Create temp directory
	tempDir, err := os.MkdirTemp("", TEMP_DIR_PREFIX)
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}

	// Create a file in it
	testFile := filepath.Join(tempDir, "test.txt")
	err = os.WriteFile(testFile, []byte("test"), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// Verify directory exists
	if _, err := os.Stat(tempDir); os.IsNotExist(err) {
		t.Fatalf("Temp directory should exist")
	}

	// Cleanup
	err = CleanupTempDir(tempDir)
	if err != nil {
		t.Fatalf("CleanupTempDir failed: %v", err)
	}

	// Verify directory was removed
	if _, err := os.Stat(tempDir); !os.IsNotExist(err) {
		t.Errorf("Temp directory should have been removed")
	}
}

func TestCleanupTempDir_InvalidPath(t *testing.T) {
	// Try to cleanup a path outside temp directory
	err := CleanupTempDir("/etc/passwd")
	if err == nil {
		t.Fatalf("CleanupTempDir should have failed for invalid path")
	}

	if !strings.Contains(err.Error(), "not allowed for cleanup") {
		t.Errorf("Expected 'not allowed for cleanup' error, got: %v", err)
	}
}
