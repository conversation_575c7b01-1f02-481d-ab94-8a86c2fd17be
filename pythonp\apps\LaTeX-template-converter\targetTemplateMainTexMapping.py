# 模板文件与主tex文件的映射
target_template_main_tex_mapping = {
    "ACM Conference Proceedings Standard Template.zip": "sample-sigconf-xelatex.tex", 
    "ACM Journals Primary Article Template.zip": "sample-manuscript.tex",  
    "CLOUD 2025.zip": "conference_101719.tex", 
    "CVPR 2022.zip": "PaperForReview.tex",  
    "CVPR 2025.zip": "main.tex",  
    "ECCV 2016.zip": "eccv2016submission.tex",  
    "EMNLP 2023.zip": "emnlp2023.tex",  
    "HPCA 2025.zip": "main.tex",  
    "ICCV 2025.zip": "main.tex",  
    "IEEE Conference standard template.zip": "IEEE-conference-template-062824.tex",  
    "ISCA 2024.zip": "conference_101719.tex",  
    "MIRCO 2025.zip": "main.tex", 
    "NeurIPS 2024.zip": "main.tex", 
    "SC 2025.zip": "sc--vis-showcase-template.tex", 

    # 继续为其他模板添加主文件映射

}

