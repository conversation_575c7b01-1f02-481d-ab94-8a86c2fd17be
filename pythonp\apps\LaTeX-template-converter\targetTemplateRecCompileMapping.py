# 模板文件与编译方式的映射
target_template_rec_compile_mapping = {
    "ACM Conference Proceedings Standard Template.zip": "xelatex -> bibtex -> xelatex*2", 
    "ACM Journals Primary Article Template.zip": "xelatex -> bibtex -> xelatex*2",  
    "CLOUD 2025.zip": "xelatex -> bibtex -> xelatex*2", 
    "CVPR 2022.zip": "xelatex -> bibtex -> xelatex*2",  
    "CVPR 2025.zip": "xelatex -> bibtex -> xelatex*2",  
    "ECCV 2016.zip": "xelatex -> bibtex -> xelatex*2",  
    "EMNLP 2023.zip": "xelatex -> bibtex -> xelatex*2",  
    "HPCA 2025.zip": "xelatex -> bibtex -> xelatex*2",  
    "ICCV 2025.zip": "xelatex -> bibtex -> xelatex*2",  
    "IEEE Conference standard template.zip": "xelatex -> bibtex -> xelatex*2",  
    "ISCA 2024.zip": "xelatex -> bibtex -> xelatex*2",  
    "MIRCO 2025.zip": "xelatex -> bibtex -> xelatex*2", 
    "NeurIPS 2024.zip": "xelatex -> bibtex -> xelatex*2", 
    "SC 2025.zip": "none", 

    # 继续为其他模板添加推荐编译方式映射

}