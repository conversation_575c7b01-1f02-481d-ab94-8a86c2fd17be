MODE=dev
PORT=8012
CLIENT_ORIGIN=http://localhost:3001

# === db ===
POSTGRES_HOST=localhost
POSTGRES_USER=einstein
POSTGRES_PASSWORD=einstein
POSTGRES_DB=einstein
POSTGRES_PORT=5432

# === bucket ===
OSS_HOST=localhost:9000
OSS_SECURE=false
OSS_ACCESS_KEY=minioadmin
OSS_SECRET_KEY=minioadmin
OSS_BUCKET=einstein-file
PROXY_MINIO=true

# === email ===
EMAIL_FROM=<EMAIL>
SMTP_HOST=smtpdm.aliyun.com
SMTP_PASS=BJt49FpFAnPY6gpd3e
SMTP_PORT=465
SMTP_USER=<EMAIL>

# === AI-Scientist ===
AI_SCIENTIST_HOST=http://***********:8000

# === Auth ===
DOMAIN=arxtect.com, localhost
TOKEN_SECRET=cd1eb380702323dd6cb3fc4054290278242d0972f9a2723f21a4e329f95584d2
ACCESS_TOKEN_MAXAGE=15
REFRESH_TOKEN_MAXAGE=10080 # 7 days
API_SECRET=lZ+noiQjUrG+i2NAsMSzC6sVL4VccVT0tQ43Gd8VtcU9jEu9eL2ciyxK1HY3IPVf

# === Admin === 
ADMIN_EMAIL=["<EMAIL>", "<EMAIL>"]
ADMIN_PASSWORD=test12312312