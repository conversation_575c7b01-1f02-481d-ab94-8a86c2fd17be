package project_controller

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/models"
	"github.com/gin-gonic/gin"
	"github.com/minio/minio-go/v7"
)

const (
	PROJECT_ASSETS_PATH  = "project-assets/"
	PROJECT_PREVIEW_PATH = "project-preview/"
	MAX_FILE_SIZE_MB     = 20
	MAX_PROJECT_SIZE_MB  = 100
)

func calculateFileHash(file multipart.File) (string, error) {
	hash := sha256.New()
	_, err := io.Copy(hash, file)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(hash.Sum(nil)), nil
}

type PreCheckRequest struct {
	Hash string `json:"hash" binding:"required"`
	Size int64  `json:"size" binding:"required"`
}

// PreCheckResult defines the result structure for the file check
type PreCheckResult struct {
	Continue bool   `json:"continue"`
	Message  string `json:"message"`
}

func (pc *ProjectController) createAsset(project *models.Project, hash string, size int64) error {
	// Check if asset in table
	var asset = models.Asset{
		Hash: hash,
		Size: size,
	}
	if err := pc.DB.FirstOrCreate(&asset, &asset).Error; err != nil {
		return fmt.Errorf("failed to save asset record to database: %w", err)
	}
	// Associate the asset with the project (skip if association already exists)
	// First check if the association already exists
	var existingAssociation int64
	if err := pc.DB.Table("project_assets").Where("project_id = ? AND asset_hash = ?", project.ID, asset.Hash).Count(&existingAssociation).Error; err == nil && existingAssociation == 0 {
		// Association doesn't exist, create it
		if err := pc.DB.Model(&project).Association("Assets").Append(&asset); err != nil {
			return fmt.Errorf("failed to associate asset with project: %w", err)
		}
	}
	// If association already exists, continue without error
	return nil
}

func (pc *ProjectController) getCurrentProjectSize(project *models.Project) (int64, error) {
	if err := pc.DB.Preload("Assets").First(&project, project.ID).Error; err != nil {
		return 0, err
	}
	var totalSize int64
	for _, asset := range project.Assets {
		totalSize += asset.Size
	}
	return totalSize + project.DocumentSize, nil
}

func (pc *ProjectController) checkIfExistBatch(hashes []string) (ExistHashesMap map[string]bool, Error error) {
	var existingAssets []models.Asset
	if err := pc.DB.Where("hash IN ?", hashes).Find(&existingAssets).Error; err != nil {
		return nil, fmt.Errorf("failed to query existing assets from database: %w", err)
	}

	// Create a map of existing hashes for quick lookup
	ExistHashesMap = make(map[string]bool)
	for _, asset := range existingAssets {
		ExistHashesMap[asset.Hash] = true
	}

	return ExistHashesMap, nil
}

type PreCheckUploadRequest struct {
	Hash string `json:"hash" binding:"required"`
	Size int64  `json:"size" binding:"required"`
}

type PreCheckUploadFileResult struct {
	Hash     string `json:"hash"`
	Continue bool   `json:"continue"`
	Error    string `json:"error"`
}

func (pc *ProjectController) PreCheckUploadBatch(ctx *gin.Context) {
	// Validate project ID
	project, err := pc.validateProjectId(ctx, ctx.Param("id"), 0)
	if err != nil {
		return
	}

	requests := make([]PreCheckUploadRequest, 0)
	if err := ctx.ShouldBindJSON(&requests); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "Invalid request body"})
		return
	}

	// Validate that we have at least one file to check
	if len(requests) == 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "At least one file must be provided"})
		return
	}

	// Get current project size once for all files
	currentProjectSize, err := pc.getCurrentProjectSize(project)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to get current project size",
		})
		return
	}

	// Calculate total upload size
	uploadSize := int64(0)
	for _, request := range requests {
		uploadSize += request.Size
	}

	// Check if total upload would exceed project size limit
	afterUploadProjectSize := currentProjectSize + uploadSize
	projectSizeExceeded := afterUploadProjectSize > MAX_PROJECT_SIZE_MB*1024*1024
	if projectSizeExceeded {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"message": fmt.Sprintf("Adding files would exceed project size limit of %d MB", MAX_PROJECT_SIZE_MB),
		})
		return
	}

	// Process each file individually
	fileResults := make([]PreCheckUploadFileResult, len(requests))
	hashes := make([]string, 0, len(requests))
	for _, request := range requests {
		hashes = append(hashes, request.Hash)
	}
	existHashesMap, err := pc.checkIfExistBatch(hashes)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to check file existence"})
		return
	}

	for i, request := range requests {
		fileResult := PreCheckUploadFileResult{
			Hash: request.Hash,
		}

		// Check individual file size limit
		if request.Size > MAX_FILE_SIZE_MB*1024*1024 {
			fileResult.Continue = false
			fileResult.Error = fmt.Sprintf("File exceeds maximum size limit of %d MB", MAX_FILE_SIZE_MB)
		} else {
			fileResult.Continue = !existHashesMap[request.Hash]
		}

		fileResults[i] = fileResult
	}

	ctx.JSON(http.StatusOK, gin.H{
		"results": fileResults,
	})
}

type UploadAssetBatchResult struct {
	Hash    string `json:"hash"`
	Success bool   `json:"success"`
	Error   string `json:"error"`
}

type UploadAssetBatchResponse struct {
	Message      string                   `json:"message"`
	Results      []UploadAssetBatchResult `json:"results"`
	SuccessCount int                      `json:"successCount"`
	TotalCount   int                      `json:"totalCount"`
}

func (pc *ProjectController) UploadAssetBatch(ctx *gin.Context) {
	// Validate project ID
	project, err := pc.validateProjectId(ctx, ctx.Param("id"), 2)
	if err != nil {
		return
	}

	// Set maximum total upload size for batch (e.g., 5 times single file limit)
	maxBatchSize := int64(MAX_FILE_SIZE_MB * 1024 * 1024)
	ctx.Request.Body = http.MaxBytesReader(ctx.Writer, ctx.Request.Body, maxBatchSize)

	if err := ctx.Request.ParseMultipartForm(maxBatchSize); err != nil {
		ctx.JSON(http.StatusRequestEntityTooLarge, gin.H{
			"message": fmt.Sprintf("Total upload size too large. Maximum allowed is %d MB", MAX_FILE_SIZE_MB),
		})
		return
	}

	// Get all files from the multipart form
	form := ctx.Request.MultipartForm
	if form == nil || form.File["files"] == nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "No files provided. Use 'files' field for multiple file upload"})
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "At least one file is required"})
		return
	}

	totalUploadSize := int64(0)
	for _, fileHeader := range files {
		totalUploadSize += fileHeader.Size
	}

	currentProjectSize, err := pc.getCurrentProjectSize(project)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to get current project size"})
		return
	}

	afterUploadProjectSize := currentProjectSize + totalUploadSize
	if afterUploadProjectSize > MAX_PROJECT_SIZE_MB*1024*1024 {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"message": fmt.Sprintf("Adding these files would exceed the project size limit of %d MB. Current project size: %.2f MB, Total upload size: %.2f MB",
				MAX_PROJECT_SIZE_MB,
				float64(currentProjectSize)/(1024*1024),
				float64(totalUploadSize)/(1024*1024)),
		})
		return
	}

	results := make([]UploadAssetBatchResult, len(files))
	successCount := 0

	hashes := make([]string, 0, len(files))
	for _, fileHeader := range files {
		hashes = append(hashes, fileHeader.Filename)
	}
	existHashesMap, err := pc.checkIfExistBatch(hashes)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to check file existence"})
		return
	}

	for i, fileHeader := range files {
		result := UploadAssetBatchResult{
			Hash: fileHeader.Filename,
		}

		// Open the file
		file, err := fileHeader.Open()
		if err != nil {
			result.Success = false
			result.Error = "Failed to open file"
			results[i] = result
			continue
		}

		// Calculate hash
		hash, err := calculateFileHash(file)
		if err != nil {
			file.Close()
			result.Success = false
			result.Error = "Failed to calculate file hash"
			results[i] = result
			continue
		}

		if hash != fileHeader.Filename {
			file.Close()
			result.Success = false
			result.Error = "File hash does not match"
			results[i] = result
			continue
		}

		// Check if file can be uploaded
		exist := existHashesMap[hash]
		if exist {
			// File already exists
			file.Close()
			result.Success = true
			results[i] = result
			successCount++
			continue
		}

		// Reset file pointer to beginning
		_, err = file.Seek(0, 0)
		if err != nil {
			file.Close()
			result.Success = false
			result.Error = "Failed to process file"
			results[i] = result
			continue
		}

		// Prepare S3 upload options
		contentType := fileHeader.Header.Get("Content-Type")
		if contentType == "" {
			contentType = "application/octet-stream"
		}

		putOpts := minio.PutObjectOptions{
			ContentType: contentType,
			UserMetadata: map[string]string{
				"hash":     hash,
				"filename": fileHeader.Filename,
			},
		}

		// Upload to S3
		_, err = pc.S3.Client.PutObject(
			ctx,
			pc.S3.BucketName,
			PROJECT_ASSETS_PATH+hash,
			file,
			fileHeader.Size,
			putOpts,
		)

		if err != nil {
			file.Close()
			result.Success = false
			result.Error = "Failed to upload file to S3"
			results[i] = result
			continue
		}

		// Create asset record in database
		if err := pc.createAsset(project, hash, fileHeader.Size); err != nil {
			file.Close()
			result.Success = false
			result.Error = "Failed to create asset record"
			results[i] = result
			continue
		}

		file.Close()
		result.Success = true
		result.Error = ""
		results[i] = result
		successCount++
	}

	// Prepare response
	response := UploadAssetBatchResponse{
		Results:      results,
		SuccessCount: successCount,
		TotalCount:   len(files),
	}

	if successCount == 0 {
		response.Message = "No files were uploaded successfully"
	} else if successCount == len(files) {
		response.Message = fmt.Sprintf("All %d files uploaded successfully", len(files))
	} else {
		response.Message = fmt.Sprintf("%d out of %d files uploaded successfully", successCount, len(files))
	}

	ctx.JSON(http.StatusOK, response)
}
