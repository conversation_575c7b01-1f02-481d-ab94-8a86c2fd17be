load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "migration",
    srcs = [
        "add_user.go",
        "migrate.go",
    ],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/cmd/migration",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/internal/config",
        "//golangp/apps/arx_center/internal/models",
        "//golangp/apps/arx_center/pkg/constants",
        "//golangp/apps/arx_center/pkg/gitea",
        "//golangp/apps/arx_center/pkg/initializers",
        "//golangp/apps/arx_center/pkg/logger",
        "//golangp/apps/arx_center/pkg/utils",
        "//golangp/apps/arx_center/pkg/xminio",
        "@com_github_google_uuid//:uuid",
        "@com_github_minio_minio_go_v7//:minio-go",
        "@com_github_spf13_cobra//:cobra",
        "@io_gorm_gorm//:gorm",
    ],
)
