package routes

import (
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/api/controllers"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/middleware"

	"github.com/gin-gonic/gin"
)

type UserRouteController struct {
	userController controllers.UserController
}

func NewRouteUserController(userController controllers.UserController) UserRouteController {

	return UserRouteController{userController}
}

func (uc *UserRouteController) UserRoute(rg *gin.RouterGroup) {

	router := rg.Group("users").Use(middleware.DeserializeUser())
	router.GET("/me", uc.userController.GetMe)

}
