"""
Bazel module definition for github.com/Arxtect/ArxBackend.
"""

module(
    name = "arxbackend",
    version = "0.1.0",
)

bazel_dep(name = "rules_go", version = "0.54.0")
bazel_dep(name = "gazelle", version = "0.43.0")
bazel_dep(name = "rules_python", version = "0.40.0")

go_deps = use_extension("@gazelle//:extensions.bzl", "go_deps")
go_deps.from_file(go_mod = "//:go.mod")
use_repo(
    go_deps,
    "com_github_alibaba_sentinel_golang",
    "com_github_alibaba_sentinel_golang_pkg_adapters_gin",
    "com_github_gin_contrib_cors",
    "com_github_gin_gonic_gin",
    "com_github_go_redis_redis_v8",
    "com_github_golang_jwt_jwt",
    "com_github_golang_jwt_jwt_v4",
    "com_github_golang_jwt_jwt_v5",
    "com_github_google_uuid",
    "com_github_k3a_html2text",
    "com_github_lib_pq",
    "com_github_magiconair_properties",
    "com_github_meilisearch_meilisearch_go",
    "com_github_minio_minio_go_v7",
    "com_github_pkoukk_tiktoken_go",
    "com_github_sashabaranov_go_openai",
    "com_github_smartwalle_alipay_v3",
    "com_github_spf13_cobra",
    "com_github_spf13_viper",
    "com_github_thanhpk_randstr",
    "com_github_toheart_functrace",
    "in_gopkg_gomail_v2",
    "io_gitea_code_sdk_gitea",
    "io_gorm_driver_postgres",
    "io_gorm_gorm",
    "org_golang_x_crypto",
    "org_golang_x_net",
)

pip = use_extension("@rules_python//python/extensions:pip.bzl", "pip")
pip.parse(
    hub_name = "pip",
    python_version = "3.12",
    requirements_lock = "//:requirements.lock",
)
use_repo(pip, "pip")

python = use_extension("@rules_python//python/extensions:python.bzl", "python")
python.toolchain(
    python_version = "3.12",
)
