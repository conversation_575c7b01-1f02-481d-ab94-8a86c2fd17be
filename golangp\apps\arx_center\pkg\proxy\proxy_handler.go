package proxy

import (
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

func CreateProxyHandler(pathPrefix, host string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the path after the prefix
		path := c.Param("path")

		// Construct the target URL
		targetURL := fmt.Sprintf("%s/%s", strings.TrimSuffix(host, "/"), strings.TrimPrefix(path, "/"))
		if c.Request.URL.RawQuery != "" {
			targetURL = fmt.Sprintf("%s?%s", targetURL, c.Request.URL.RawQuery)
		}
		fmt.Printf("Proxying request to: %s\n", targetURL)
		// Create a new HTTP request
		req, err := http.NewRequest(c.Request.Method, targetURL, c.Request.Body)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create proxy request"})
			return
		}

		// Copy headers from original request
		for key, values := range c.Request.Header {
			for _, value := range values {
				req.Header.Add(key, value)
			}
		}

		// Make the request to the target host
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to proxy request"})
			return
		}
		defer resp.Body.Close()

		// Copy response headers
		for key, values := range resp.Header {
			for _, value := range values {
				c.Header(key, value)
			}
		}

		// Set the status code
		c.Status(resp.StatusCode)

		// Stream the response body
		_, err = io.Copy(c.Writer, resp.Body)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to stream response"})
			return
		}
	}
}

func OSSProxyHandler(ctx *gin.Context) {
	encodedURL := ctx.Query("target")
	decodedURL, err := url.QueryUnescape(encodedURL)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "Invalid URL"})
		return
	}

	objectUrl, err := url.Parse(decodedURL)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"message": "Invalid URL"})
		return
	}

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// Make request to the presigned URL
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, objectUrl.String(), nil)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to create request"})
		return
	}

	resp, err := client.Do(req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to fetch file"})
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		ctx.JSON(resp.StatusCode, gin.H{"message": "Failed to fetch file from storage"})
		return
	}

	// Copy headers from S3 response to our response
	for key, values := range resp.Header {
		for _, value := range values {
			ctx.Header(key, value)
		}
	}

	// Stream the file to the client
	ctx.Status(resp.StatusCode)
	_, err = io.Copy(ctx.Writer, resp.Body)
	if err != nil {
		// Note: Headers might have been already sent at this point
		ctx.Status(http.StatusInternalServerError)
		return
	}
}
