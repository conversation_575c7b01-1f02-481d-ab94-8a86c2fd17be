load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "openai-config",
    srcs = [
        "cli.go",
        "openai-config.go",
    ],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/openai-config",
    visibility = ["//visibility:public"],
)

go_test(
    name = "openai-config_test",
    srcs = ["openai-config_test.go"],
    embed = [":openai-config"],
)
