#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile --output-file=requirements.lock requirements.txt
#
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via starlette
click==8.1.8
    # via uvicorn
colorama==0.4.6
    # via click
fastapi==0.115.6
    # via -r requirements.txt
h11==0.16.0
    # via uvicorn
idna==3.10
    # via anyio
pydantic==2.11.4
    # via fastapi
pydantic-core==2.33.2
    # via pydantic
python-multipart==0.0.20
    # via -r requirements.txt
sniffio==1.3.1
    # via anyio
starlette==0.41.3
    # via fastapi
typing-extensions==4.13.2
    # via
    #   anyio
    #   fastapi
    #   pydantic
    #   pydantic-core
    #   typing-inspection
typing-inspection==0.4.0
    # via pydantic
uvicorn==0.30.6
    # via -r requirements.txt
