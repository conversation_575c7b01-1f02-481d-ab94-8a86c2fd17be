package controllers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/models"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/gitea"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/logger"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type UserController struct {
	DB *gorm.DB
}

func NewUserController(DB *gorm.DB) UserController {
	return UserController{DB: DB}
}

func (uc *UserController) GetMe(ctx *gin.Context) {
	currentUser := ctx.MustGet("currentUser").(models.User)

	userResponse := &models.UserResponse{
		ID:        currentUser.ID,
		Name:      currentUser.Name,
		Email:     currentUser.Email,
		Photo:     currentUser.Photo,
		Role:      currentUser.Role,
		Verified:  currentUser.Verified,
		Balance:   currentUser.Balance,
		Provider:  currentUser.Provider,
		CreatedAt: currentUser.CreatedAt,
		UpdatedAt: currentUser.UpdatedAt,
	}

	ctx.JSON(http.StatusOK, gin.H{"status": "success", "data": gin.H{"user": userResponse}})
}

func (uc *UserController) GetUserAccessTokens(c *gin.Context) {
	inputToken := c.Query("token")
	user := c.MustGet("currentUser").(models.User)

	fmt.Println(user.Name, user.Password)

	client, err := gitea.CreateGiteaUserClient(user.Name, user.Password)
	if err != nil || client == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"status": "fail", "message": err.Error()})
		return
	}

	if inputToken != "" {
		isValid, err := client.ValidateAccessToken(inputToken)
		if err != nil {
			logger.Warning(err.Error())
		}
		if isValid {
			c.JSON(http.StatusOK, gin.H{"status": "success", "data": inputToken})
			return
		}
	}

	_, token, err := client.CreateAccessToken()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"status": "fail", "message": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"status": "success", "data": token})
}

func (uc *UserController) CreateGiteaRepo(c *gin.Context) {
	var payload gitea.CreateRepoOption
	if err := c.ShouldBindJSON(&payload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": err.Error()})
		return
	}

	user := c.MustGet("currentUser").(models.User)
	client, err := gitea.CreateGiteaUserClient(user.Name, user.Password)
	if err != nil || client == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"status": "fail", "message": err.Error()})
		return
	}
	repo, err := client.CreateRepo(payload.Name, payload.Description, payload.Private)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"status": "fail", "message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"status": "success", "data": repo})
}

func (uc *UserController) GetUserRepoList(c *gin.Context) {
	user := c.MustGet("currentUser").(models.User)

	// 使用 DefaultQuery 方法设置默认值
	page := c.DefaultQuery("page", "1")
	perPage := c.DefaultQuery("limit", "10")
	pageInt, err := strconv.Atoi(page)
	if err != nil {
		c.JSON(400, gin.H{"message": "Invalid page number"})
		return
	}

	perPageInt, err := strconv.Atoi(perPage)
	if err != nil {
		c.JSON(400, gin.H{"message": "Invalid limit"})
		return
	}
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"status": "fail", "message": err.Error()})
		return
	}

	client, err := gitea.CreateGiteaUserClient(user.Name, user.Password)

	if err != nil || client == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"status": "fail", "message": err.Error()})
		return
	}

	repos, total, err := client.ListUserRepos(pageInt, perPageInt)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"status": "fail", "message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"status": "success", "data": repos, "total": total})
}

func (uc *UserController) ValidateAccessToken(c *gin.Context) {
	user := c.MustGet("currentUser").(models.User)

	token := c.Param("token")
	if token == "" {
		c.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": "token is required"})
		return
	}

	client, err := gitea.CreateGiteaUserClient(user.Name, user.Password)

	if err != nil || client == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"status": "fail", "message": err.Error()})
		return
	}

	isValid, err := client.ValidateAccessToken(token)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"status": "fail", "message": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"status": "success", "data": isValid})
}

func (uc *UserController) DeleteGiteaRepo(c *gin.Context) {
	repoName := c.Param("name")
	if repoName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"status": "fail", "message": "name is required"})
		return
	}

	user := c.MustGet("currentUser").(models.User)
	client, err := gitea.CreateGiteaUserClient(user.Name, user.Password)
	if err != nil || client == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"status": "fail", "message": err.Error()})
		return
	}
	err = client.DeleteRepo(user.Name, repoName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"status": "fail", "message": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"status": "success"})
}
