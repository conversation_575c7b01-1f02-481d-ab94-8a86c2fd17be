package file

import (
	"fmt"
	"os"
	"path/filepath"
)

// LocateFile searches for the given relative file or directory name starting
// from the current working directory and moving upward through parent directories.
// If the file or folder is found, it returns the absolute path. If not found,
// it returns an error.
//
// Example usage:
//
//	path, err := LocateFile("src/config.yaml")
//	path, err := LocateFile("src/a_folder")

func LocateFile(target string) (string, error) {
	wd, err := os.Getwd()
	if err != nil {
		return "", err
	}

	for {
		configPath := filepath.Join(wd, target)
		if _, err := os.Stat(configPath); err == nil {
			return configPath, nil
		}

		parentDir := filepath.Dir(wd)
		if parentDir == wd || wd == "/" {
			return "", fmt.Errorf("target '%s' not found", target)
		}

		wd = parentDir
	}
}
