package gitea

import (
	"fmt"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/config"

	"code.gitea.io/sdk/gitea"
)

type GiteaAdminClient struct {
	Host     string
	User     string
	Password string
	Client   *gitea.Client
}

func CreateGiteaAdminClient() (*GiteaAdminClient, error) {

	configCopy := config.Config

	client, err := gitea.NewClient(configCopy.GiteaHost, gitea.SetBasicAuth(configCopy.GiteaAdminUser, configCopy.GiteaAdminPassword))
	if err != nil {
		return nil, fmt.Errorf("failed to create admin client: %v", err)
	}

	return &GiteaAdminClient{Host: configCopy.GiteaHost, User: configCopy.GiteaAdminUser, Password: configCopy.GiteaAdminPassword, Client: client}, nil
}

func GetGiteaAdminClient() (*gitea.Client, error) {

	adminClient, err := CreateGiteaAdminClient()
	if err != nil {
		return nil, err
	}

	return adminClient.Client, nil
}
