services:
  postgres:
    image: postgres:17.4
    environment:
      POSTGRES_USER: einstein
      POSTGRES_PASSWORD: einstein
      POSTGRES_DB: einstein
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data

  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"   # Web interface / API
      - "9001:9001"   # Admin console
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    volumes:
      - minio-data:/data
    command: server --console-address ":9001" /data

volumes:
  postgres-data:
  minio-data:
