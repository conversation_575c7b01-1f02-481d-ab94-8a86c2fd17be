# AI Template Conversion Feature

This document describes the AI-powered LaTeX template conversion functionality implemented in the arx_center module.

## Overview

The template conversion feature allows users to:
1. Upload LaTeX templates to the system
2. Convert their LaTeX projects from one template format to another using AI
3. Download the converted results

## Architecture

### Components

1. **Models** (`internal/models/`)
   - `Template`: Stores template metadata and file information
   - `TemplateConversion`: Tracks conversion tasks and their status

2. **Controllers** (`api/controllers/`)
   - `TemplateConversionController`: Handles template upload and conversion requests

3. **Utilities** (`pkg/`)
   - `ziputils`: ZIP file handling utilities
   - `template_tools`: AI agent tool functions for file operations

4. **Routes** (`api/routes/`)
   - Template management endpoints
   - Conversion task endpoints

## API Endpoints

### Template Management

#### Upload Template
```
POST /api/v1/templates
Content-Type: multipart/form-data

Form fields:
- template: ZIP file containing LaTeX template
- name: Template name
- description: Template description
- category: Template category (e.g., "conference", "journal")
- version: Template version
- author: Template author
- license: Template license
- tags: JSON array of tags
- isPublic: Whether template is public
```

#### Get Templates
```
GET /api/v1/templates
```

#### Get Template by ID
```
GET /api/v1/templates/{id}
```

### Template Conversion

#### Start Conversion
```
POST /api/v1/conversions
Content-Type: multipart/form-data

Form fields:
- source: ZIP file containing source LaTeX project
- taskName: Name for the conversion task
- targetTemplateId: UUID of target template
```

#### Get Conversions
```
GET /api/v1/conversions
```

#### Get Conversion by ID
```
GET /api/v1/conversions/{id}
```

#### Download Result
```
GET /api/v1/conversions/{id}/download
```

## AI Agent Integration

The system integrates with Dify AI platform to perform intelligent template conversion. The AI agent has access to the following tools:

### Tool Functions

1. **list_source_files(path)**: List files in source project
2. **view_source_file(file_path)**: Read source file content
3. **modify_source_file(file_path, content)**: Modify source file
4. **create_source_file(file_path, content)**: Create new source file
5. **delete_source_file(file_path)**: Delete source file
6. **list_target_template_files(path)**: List template files
7. **view_target_template_file(file_path)**: Read template file
8. **copy_file(source_path, dest_path)**: Copy template file to source
9. **check_compilation()**: Attempt LaTeX compilation
10. **complete_task()**: Mark conversion as complete

### AI Agent Workflow

1. Agent analyzes source project structure
2. Agent examines target template format
3. Agent performs step-by-step conversion:
   - Modifies document class and packages
   - Updates formatting and styling
   - Adjusts content structure
   - Copies necessary template files
4. Agent validates conversion with compilation check
5. Agent marks task as complete

## Configuration

### Required Environment Variables

```yaml
# Dify AI Platform
DIFY_HOST: "https://api.dify.ai"
DIFY_KEY: "your-dify-api-key"
DIFY_CONSOLE_UMAIL: "<EMAIL>"
DIFY_CONSOLE_PASSWORD: "your-password"

# Object Storage
OSS_HOST: "localhost:9000"
OSS_ACCESS_KEY: "minioadmin"
OSS_SECRET_KEY: "minioadmin"
OSS_BUCKET: "arx-center"
```

### Dify App Configuration

The system uses a specific Dify app with key: `app-aDvCsdCrTwmZVpYnk7FnuAKC`

This app should be configured with:
- Agent mode enabled
- Tool functions for file operations
- LaTeX template conversion expertise
- Proper prompt for conversion tasks

## File Storage Structure

```
S3/MinIO Bucket:
├── template-files/
│   └── {hash} - Template ZIP files
├── conversion-files/
│   ├── source_{hash} - Source ZIP files
│   └── result_{hash} - Result ZIP files
```

## Database Schema

### Templates Table
- id (UUID, Primary Key)
- name (String)
- description (Text)
- category (String)
- file_hash (String, Unique)
- file_size (BigInt)
- file_name (String)
- version (String)
- author (String)
- license (String)
- tags (JSON)
- is_active (Boolean)
- is_public (Boolean)
- owner_id (UUID, Foreign Key)
- usage_count (BigInt)
- created_at (Timestamp)
- updated_at (Timestamp)

### Template Conversions Table
- id (UUID, Primary Key)
- task_name (String)
- status (Enum: pending, processing, completed, failed, cancelled)
- source_file_hash (String)
- source_file_name (String)
- source_file_size (BigInt)
- target_template_id (UUID, Foreign Key)
- result_file_hash (String)
- result_file_name (String)
- result_file_size (BigInt)
- dify_conversation_id (String)
- dify_task_id (String)
- processing_log (Text)
- error_message (Text)
- started_at (Timestamp)
- completed_at (Timestamp)
- processing_time_ms (BigInt)
- user_id (UUID, Foreign Key)
- metadata (JSON)
- created_at (Timestamp)
- updated_at (Timestamp)

## Usage Example

1. **Upload a template:**
   ```bash
   curl -X POST http://localhost:8080/api/v1/templates \
     -H "Authorization: Bearer {token}" \
     -F "template=@template.zip" \
     -F "name=CVPR Template" \
     -F "description=IEEE CVPR conference template" \
     -F "category=conference" \
     -F "isPublic=true"
   ```

2. **Start conversion:**
   ```bash
   curl -X POST http://localhost:8080/api/v1/conversions \
     -H "Authorization: Bearer {token}" \
     -F "source=@my_paper.zip" \
     -F "taskName=Convert to CVPR format" \
     -F "targetTemplateId={template-uuid}"
   ```

3. **Check conversion status:**
   ```bash
   curl -X GET http://localhost:8080/api/v1/conversions/{conversion-id} \
     -H "Authorization: Bearer {token}"
   ```

4. **Download result:**
   ```bash
   curl -X GET http://localhost:8080/api/v1/conversions/{conversion-id}/download \
     -H "Authorization: Bearer {token}"
   ```

## Error Handling

The system provides comprehensive error handling for:
- Invalid ZIP files
- File size limits
- AI processing failures
- Storage errors
- Authentication issues

## Security Considerations

- All file operations are sandboxed within temporary directories
- Path traversal attacks are prevented
- File size limits are enforced
- User authentication is required for all operations
- Template access is controlled by ownership and public flags
