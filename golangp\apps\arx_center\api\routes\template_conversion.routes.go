package routes

import (
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/api/controllers"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/middleware"

	"github.com/gin-gonic/gin"
)

type TemplateConversionRouteController struct {
	templateConversionController controllers.TemplateConversionController
}

func NewTemplateConversionRouteController(templateConversionController controllers.TemplateConversionController) TemplateConversionRouteController {
	return TemplateConversionRouteController{templateConversionController}
}

func (tcrc *TemplateConversionRouteController) TemplateConversionRoute(rg *gin.RouterGroup) {
	// Template management routes
	templateRouter := rg.Group("templates").Use(middleware.DeserializeUser())
	{
		// Template CRUD operations
		templateRouter.POST("", tcrc.templateConversionController.UploadTemplate)
		templateRouter.GET("", tcrc.templateConversionController.GetTemplates)
		templateRouter.GET("/:id", tcrc.templateConversionController.GetTemplate)
	}

	// Template conversion routes
	conversionRouter := rg.Group("conversions").Use(middleware.DeserializeUser())
	{
		// Conversion operations
		conversionRouter.POST("", tcrc.templateConversionController.StartConversion)
		conversionRouter.GET("", tcrc.templateConversionController.GetConversions)
		conversionRouter.GET("/:id", tcrc.templateConversionController.GetConversion)
		conversionRouter.GET("/:id/download", tcrc.templateConversionController.DownloadResult)
	}
}
