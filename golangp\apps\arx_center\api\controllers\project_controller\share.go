package project_controller

import (
	"fmt"
	"net/http"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/config"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/models"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/constants"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/utils"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

func (controller *ProjectController) GetProjectMembers(c *gin.Context) {
	project, err := controller.validateProjectId(c, c.Param("id"), 2)
	if err != nil {
		return
	}

	var members []models.ProjectMember
	if err := controller.DB.Where("project_id = ?", project.ID).Preload("User").Find(&members).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to get project members"})
		return

	}

	c.JSON(http.StatusOK, members)
}

type InvitationTokenPayload struct {
	ProjectID      string `json:"p"`
	Role           string `json:"r"`
	ExclusiveEmail string `json:"e"`
	jwt.RegisteredClaims
}

type CreateTokenPayload struct {
	Role           string `json:"role" binding:"required"`
	ExclusiveEmail string `json:"exclusiveEmail"`
}

/**
 * @api {post} /projects/:id/share-tokens Create Share Token
 * @apiName CreateShareToken
 * @apiGroup ProjectShare
 * @apiDescription Create a share token for a project.
 */
func (controller *ProjectController) CreateShareToken(c *gin.Context) {
	user := c.MustGet("currentUser").(models.User)
	var payload CreateTokenPayload
	if err := c.ShouldBindJSON(&payload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": "Invalid input"})
		return
	}
	if payload.Role != constants.ProjectRoleCollaborator && payload.Role != constants.ProjectRoleViewer {
		c.JSON(http.StatusBadRequest, gin.H{"message": "Invalid role"})
		return
	}
	project, err := controller.validateProjectId(c, c.Param("id"), 3)
	if err != nil {
		return
	}

	invitationTokenPayload := InvitationTokenPayload{
		ProjectID:      project.ID.String(),
		Role:           payload.Role,
		ExclusiveEmail: payload.ExclusiveEmail,
	}

	token, err := utils.CreateToken(invitationTokenPayload)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to generate invitation token"})
		return
	}

	if payload.ExclusiveEmail == "" {
		projectUpdate := map[string]interface{}{}
		if payload.Role == constants.ProjectRoleCollaborator {
			projectUpdate["collaborator_share_token"] = token
		} else {
			projectUpdate["viewer_share_token"] = token
		}

		if err := controller.DB.Model(&project).Updates(&projectUpdate).Error; err != nil {
			fmt.Printf("Failed to update project share token: %v\n", err)
			c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to save project"})
			return
		}
		c.JSON(http.StatusOK, gin.H{"token": token})
		return
	} else {
		targetUser := models.User{}
		if err := controller.DB.Where("email = ?", payload.ExclusiveEmail).First(&targetUser).Error; err != nil {
			if err != gorm.ErrRecordNotFound {
				c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to check user existence"})
				return
			}
		}

		var username string
		if targetUser.ID == uuid.Nil {
			username = payload.ExclusiveEmail
		} else {
			username = targetUser.Name
		}

		go utils.SendShareProjectEmail(payload.ExclusiveEmail, &utils.ShareProjectEmailData{
			Subject:        fmt.Sprintf("ArxTect - You are invited to a project: %s", project.ProjectName),
			AuthorizedUser: username,
			SharerEmail:    user.Email,
			ProjectName:    project.ProjectName,
			ProjectLink:    config.Config.ClientOrigin + "/invitation?token=" + token,
		}, "shareProject.html")

		c.JSON(http.StatusOK, gin.H{"message": "Invitation sent successfully"})
		return
	}
}

func (controller *ProjectController) GetPublicTokens(c *gin.Context) {
	project, err := controller.validateProjectId(c, c.Param("id"), 3)
	if err != nil {
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"collaboratorShareToken": project.CollaboratorShareToken,
		"viewerShareToken":       project.ViewerShareToken,
	})
}

func (controller *ProjectController) GetTokenInfo(c *gin.Context) {
	user := c.MustGet("currentUser").(models.User)
	token := c.Param("token")
	invitationPayload := InvitationTokenPayload{}
	err := utils.ValidateToken(token, &invitationPayload)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": "Invalid token:" + err.Error()})
		return
	}
	fmt.Printf("Invitation Payload: %+v\n", invitationPayload)
	if invitationPayload.ExclusiveEmail != "" && invitationPayload.ExclusiveEmail != user.Email {
		c.JSON(http.StatusBadRequest, gin.H{"message": "You are not the invitee, please check your email for the invitation."})
		return
	}

	project, err := controller.validateProjectId(c, invitationPayload.ProjectID, 0)
	if err != nil {
		return
	}

	// Return success response
	c.JSON(http.StatusOK, gin.H{
		"role":        invitationPayload.Role,
		"projectId":   project.ID,
		"projectName": project.ProjectName,
	})
}

type AddProjectMemberPayload struct {
	Token string `json:"token" binding:"required"`
}

func (controller *ProjectController) AcceptProjectInvitation(c *gin.Context) {
	currentUser := c.MustGet("currentUser").(models.User)
	project, err := controller.validateProjectId(c, c.Param("id"), 0)
	if err != nil {
		return
	}
	fmt.Printf("Current project: %+v\n", project)
	var payload AddProjectMemberPayload
	if err := c.ShouldBindJSON(&payload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": "Invalid input"})
		return
	}
	token := payload.Token
	invitationToken := InvitationTokenPayload{}
	err = utils.ValidateToken(token, &invitationToken)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": "Invalid token: " + err.Error()})
		return
	}

	if invitationToken.ExclusiveEmail != "" && invitationToken.ExclusiveEmail != currentUser.Email {
		c.JSON(http.StatusBadRequest, gin.H{"message": "You are not the invitee, please check your email for the invitation."})
		return
	}

	if invitationToken.ExclusiveEmail == "" && token != project.CollaboratorShareToken && token != project.ViewerShareToken {
		c.JSON(http.StatusBadRequest, gin.H{"message": "Invalid token for this project"})
		return
	}

	if invitationToken.ProjectID != project.ID.String() {
		c.JSON(http.StatusBadRequest, gin.H{"message": "Token does not belong to this project"})
		return
	}

	if currentUser.ID == project.OwnerID {
		c.JSON(http.StatusBadRequest, gin.H{"message": "Owner cannot be added as a member"})
		return
	}

	member := models.ProjectMember{
		UserID:    currentUser.ID,
		ProjectID: project.ID,
		Role:      invitationToken.Role,
	}

	controller.DB.Save(&member)

	c.JSON(http.StatusOK, gin.H{"message": "Invitation accepted successfully"})
}

func (controller *ProjectController) GetProjectAccess(c *gin.Context) {
	user := c.MustGet("currentUser").(models.User)
	project, err := controller.validateProjectId(c, c.Param("id"), 0)
	if err != nil {
		return
	}

	body := gin.H{
		"user": gin.H{
			"id":   user.ID,
			"name": user.Name,
		},
		"readOnly": true,
	}

	if project.Published {
		c.JSON(http.StatusOK, body)
		return
	}

	if user.Role == constants.AppRoleAdmin {
		body["readOnly"] = false
		c.JSON(http.StatusOK, body)
		return
	}

	// Check if the user is the owner
	if project.OwnerID == user.ID {
		body["readOnly"] = false
		c.JSON(http.StatusOK, body)
		return
	}

	// Check if the user is a member
	var member models.ProjectMember
	if err := controller.DB.Where("user_id = ? AND project_id = ?", c.MustGet("currentUser").(models.User).ID, project.ID).First(&member).Error; err != nil {
		c.JSON(http.StatusForbidden, gin.H{
			"message": "You do not have access to this project",
		})
		return
	}

	body["readOnly"] = member.Role != constants.ProjectRoleCollaborator
	c.JSON(http.StatusOK, body)
}

func (controller *ProjectController) RemoveMembers(c *gin.Context) {
	project, err := controller.validateProjectId(c, c.Param("id"), 3)
	if err != nil {
		return
	}

	userId := c.Param("userId")
	if userId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"message": "User ID is required"})
		return
	}
	var member models.ProjectMember
	if err := controller.DB.Where("user_id = ? AND project_id = ?", userId, project.ID).Delete(&member).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to remove member"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Members removed successfully"})
}
