package server

import (
	"log"
	"net/http"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/config"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/services/oss"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/api/controllers"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/api/controllers/project_controller"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/api/routes"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/cmd/migration/motest"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/initializers"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/middleware"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/proxy"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
)

var (
	server *gin.Engine
)

var (
	configYml string
	StartCmd  = &cobra.Command{
		Use:          "server",
		Short:        "Start API server",
		Example:      "ArxBackend server -c config/settings-dev.yml",
		SilenceUsage: true,
		PreRun: func(cmd *cobra.Command, args []string) {
			setup()
		},
		RunE: func(cmd *cobra.Command, args []string) error {
			return run()
		},
	}
)

func init() {
	StartCmd.PersistentFlags().StringVarP(&configYml, "config", "c", "config/settings-dev.yml", "Start server with provided configuration file")
}

func setup() {
	log.Println("🚗 Load configuration file ...")
	err := config.LoadConfig(configYml)
	if err != nil {
		log.Println("🚗 Local config loaded failed", err)
		return
	}

	initializers.ConnectDB(&config.Config)
	initializers.ConnectS3(&config.Config)
	// initializers.InitRedisClient(&config.Env)
	// initializers.InitMeiliClient(&config.Env)
	log.Println("🚗 Initialize is success....", config.Config.Mode)

}

func run() error {

	server = gin.Default()

	corsConfig := cors.DefaultConfig()
	corsConfig.AllowAllOrigins = true
	corsConfig.AllowCredentials = true

	server.Use(cors.New(corsConfig))
	server.Use(middleware.ErrorHandler())
	server.Use(gin.Recovery())

	// TODO: refactor and use this
	ossService, err := oss.NewOssService(&config.Config)
	if err != nil {
		log.Fatal("🚗 Failed to initialize OSS service:", err)
	}

	router := server.Group("/api/v1")
	router.GET("/healthcheck", func(ctx *gin.Context) {
		ctx.JSON(http.StatusOK, gin.H{"status": "success", "message": "Welcome to Einstein!"})
	})

	AuthController := controllers.NewAuthController(initializers.DB)
	AuthRouteController := routes.NewAuthRouteController(AuthController)
	AuthRouteController.AuthRoute(router)

	UserController := controllers.NewUserController(initializers.DB)
	UserRouteController := routes.NewRouteUserController(UserController)
	UserRouteController.UserRoute(router)

	ProjectController := project_controller.NewProjectController(initializers.DB, initializers.S3, ossService)
	ProjectRouteController := routes.NewProjectRouteController(ProjectController)
	ProjectRouteController.ProjectkRoute(router)

	// Template Conversion Controller and Routes
	TemplateConversionController := controllers.NewTemplateConversionController(initializers.DB, initializers.S3)
	TemplateConversionRouteController := routes.NewTemplateConversionRouteController(TemplateConversionController)
	TemplateConversionRouteController.TemplateConversionRoute(router)

	if config.Config.OSSProxy && config.Config.OSSSecure {
		router.GET("/oss-service/*path", proxy.CreateProxyHandler("/oss-service", "https://"+config.Config.OSSHost))
	} else if config.Config.OSSProxy {
		router.GET("/oss-service/*path", proxy.CreateProxyHandler("/oss-service", "http://"+config.Config.OSSHost))
	}

	router.Any("/ai-scientist/*path", proxy.CreateProxyHandler("/ai-scientist", config.Config.AiScientistHost))

	if config.Config.Mode == "test" {
		// 先迁移所有的表
		motest.TestModeMigrate()
		log.Println("🚗 Initialize data creation is success....", config.Config.Mode)
	}

	log.Println("🚗 api server is starting....", config.Config.Mode)
	log.Fatal(server.Run(":" + config.Config.Port))
	return nil
}
