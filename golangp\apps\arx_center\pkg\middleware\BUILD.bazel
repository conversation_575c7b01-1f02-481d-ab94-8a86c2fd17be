load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "middleware",
    srcs = [
        "api_auth.go",
        "deserialize-user.go",
        "error_handler.go",
        "header.go",
        "sentinel.go",
    ],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/middleware",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/internal/config",
        "//golangp/apps/arx_center/internal/models",
        "//golangp/apps/arx_center/pkg/initializers",
        "//golangp/apps/arx_center/pkg/utils",
        "@com_github_alibaba_sentinel_golang//core/system",
        "@com_github_alibaba_sentinel_golang_pkg_adapters_gin//:gin",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@com_github_google_uuid//:uuid",
    ],
)
