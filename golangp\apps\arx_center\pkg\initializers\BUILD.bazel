load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "initializers",
    srcs = [
        "config_struct.go",
        "connectDB.go",
        "connectMeili.go",
        "connectRedis.go",
        "connectS3.go",
    ],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/initializers",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/internal/config",
        "//golangp/apps/arx_center/pkg/logger",
        "//golangp/apps/arx_center/pkg/xminio",
        "@com_github_go_redis_redis_v8//:redis",
        "@com_github_meilisearch_meilisearch_go//:meilisearch-go",
        "@io_gorm_driver_postgres//:postgres",
        "@io_gorm_gorm//:gorm",
    ],
)
