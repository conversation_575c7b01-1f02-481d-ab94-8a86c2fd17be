load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "routes",
    srcs = [
        "auth.routes.go",
        "chat.routes.go",
        "conversations.routes.go",
        "project.routes.go",
        "template_conversion.routes.go",
        "user.routes.go",
    ],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/api/routes",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/api/controllers",
        "//golangp/apps/arx_center/api/controllers/project_controller",
        "//golangp/apps/arx_center/pkg/middleware",
        "@com_github_gin_gonic_gin//:gin",
    ],
)
