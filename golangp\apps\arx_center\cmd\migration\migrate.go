package migration

import (
	"context"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/config"
	"github.com/google/uuid"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/models"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/constants"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/initializers"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/logger"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/utils"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/xminio"
	"github.com/minio/minio-go/v7"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
)

var configYml string

// Add MigrateCmd
var MigrateCmd = &cobra.Command{
	Use:          "migrate",
	Short:        "Run database migrations",
	Example:      "ArxBackend migrate -c config/settings.yml",
	SilenceUsage: true,
	PreRun: func(cmd *cobra.Command, args []string) {
		setupMigrate()
	},
	RunE: func(cmd *cobra.Command, args []string) error {
		return migrate()
	},
}

func init() {
	MigrateCmd.PersistentFlags().StringVarP(&configYml, "config", "c", "config/settings.yml", "Run migrations with provided configuration file")
}

func setupMigrate() {
	log.Println("🚗 Loading configuration for migration...")
	err := config.LoadConfig(configYml)
	if err != nil {
		log.Printf("❌ Failed to load configuration: %v", err)
		return
	}
	log.Printf("✅ Configuration loaded successfully in %s mode", config.Config.Mode)

	initializers.ConnectDB(&config.Config)
	log.Printf("✅ Database connection established in %s mode", config.Config.Mode)
}

func removeAllAdmins(DB *gorm.DB) {
	var adminUsers []models.User
	res := DB.Find(&adminUsers, "role = ?", constants.AppRoleAdmin)
	if res.Error != nil {
		logger.Warning("Error finding admin users %s", res.Error.Error())
	}

	for _, adminUser := range adminUsers {
		userToDelete := adminUser.Email
		res := DB.Where("id = ?", adminUser.ID).Delete(&models.User{})
		if res.Error != nil {
			logger.Warning("Error deleting admin user %s: %s", userToDelete, res.Error.Error())
		}
		logger.Info("Previous admin user %s deleted successfully", userToDelete)
	}
}

func SetupAdmin(DB *gorm.DB) {
	// kong.Parse(&openai_config.CLI)
	// OpenaiConfig := openai_config.LoadOpenAIConfig()
	// adminPassword := OpenaiConfig.AdminPassword
	config := config.Config
	adminPassword := config.AdminPassword

	hashedPassword, err := utils.HashPassword(adminPassword)
	if err != nil {
		logger.Danger("Error hashing password %s", err.Error())
	}
	removeAllAdmins(DB)

	for index, adminEmail := range config.AdminEmail {
		now := time.Now()
		newUser := models.User{
			ID:        uuid.New(),
			Name:      "Admin" + strconv.Itoa(index),
			Email:     strings.ToLower(adminEmail),
			Password:  hashedPassword,
			Role:      constants.AppRoleAdmin,
			Verified:  true,
			Photo:     "test",
			Provider:  "local",
			CreatedAt: now,
			UpdatedAt: now,
		}

		var adminUser models.User
		res := DB.First(&adminUser, "email = ?", adminEmail)
		if res.Error != nil {
			logger.Info("Admin user %s does not exist, creating one", adminEmail)
		} else {
			res := DB.Delete(&adminUser)
			if res.Error != nil {
				logger.Warning("Error deleting exist admin user %s", res.Error.Error())
			}
			logger.Info("Existing Admin user deleted successfully")
		}

		result := DB.Create(&newUser)

		if result.Error != nil && strings.Contains(result.Error.Error(), "duplicated key not allowed") {
			logger.Warning("Admin email already exists")
			return
		} else if result.Error != nil {
			logger.Danger("Error creating admin user", result.Error)
		}

		logger.Info("Admin user %s created successfully", adminEmail)
	}

}

func setupMinIOBucket() error {
	log.Println("🔄 Setting up MinIO bucket...")

	s3 := xminio.NewS3Manager(
		config.Config.OSSBucket,
		config.Config.OSSAccessKey,
		config.Config.OSSSecretKey,
		config.Config.OSSHost,
	)

	ctx := context.Background()
	exists, err := s3.Client.BucketExists(ctx, s3.BucketName)
	if err != nil {
		log.Printf("❌ Error checking bucket existence: %v", err)
		return err
	}

	if !exists {
		err = s3.Client.MakeBucket(ctx, s3.BucketName, minio.MakeBucketOptions{})
		if err != nil {
			if strings.Contains(err.Error(), "Your previous request to create the named bucket succeeded") {
				log.Printf("✅ Bucket %s already exists", s3.BucketName)
				return nil
			}
			log.Printf("❌ Error creating bucket: %v", err)
			return err
		}
		log.Printf("✅ Successfully created bucket: %s", s3.BucketName)
	} else {
		log.Printf("✅ Bucket %s already exists", s3.BucketName)
	}

	return nil
}

func migrate() error {
	log.Println("🚀 Starting database migrations...")

	// Setup MinIO bucket
	err := setupMinIOBucket()
	if err != nil {
		log.Printf("❌ MinIO bucket setup failed: %v", err)
		return err
	}

	// Run your migrations here
	err = initializers.DB.AutoMigrate(
		&models.User{},
		&models.RefreshToken{},
		&models.Project{},
		&models.ProjectMember{},
		&models.Asset{},
	)
	if err != nil {
		log.Printf("❌ Migration failed: %v", err)
		return err
	}

	err = initializers.DB.SetupJoinTable(&models.Project{}, "Members", &models.ProjectMember{})
	if err != nil {
		log.Printf("❌ SetupJoinTable failed: %v", err)
		return err
	}

	err = initializers.DB.SetupJoinTable(&models.User{}, "SharedProjects", &models.ProjectMember{})
	if err != nil {
		log.Printf("❌ SetupJoinTable failed: %v", err)
		return err
	}

	SetupAdmin(initializers.DB)
	User(initializers.DB)
	log.Println("✅ Database migrations completed successfully")

	return nil
}
