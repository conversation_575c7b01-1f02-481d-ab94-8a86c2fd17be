package utils

import (
	"bytes"
	"html/template"
	"log"
	"os"
	"path/filepath"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/config"
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/file"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/models"

	"github.com/k3a/html2text"
	"gopkg.in/gomail.v2"
)

type AccountEmailData struct {
	URL              string
	VerificationCode string
	FirstName        string
	Subject          string
	Amount           int64
	Balance          int64
}

type ShareProjectEmailData struct {
	AuthorizedUser string
	SharerUser     string
	SharerEmail    string
	ProjectName    string
	ProjectLink    string
	Subject        string
}

func ParseTemplateDir(dir string) (*template.Template, error) {

	var paths []string
	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			paths = append(paths, path)
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	return template.ParseFiles(paths...)
}

func SendEmail(emailTo string, subject string, data any, emailTemp string) {

	configCopy := config.Config

	from := "Notification <" + configCopy.EmailFrom + ">"
	smtpPass := configCopy.SMTPPass
	smtpUser := configCopy.SMTPUser
	to := emailTo
	smtpHost := configCopy.SMTPHost
	smtpPort := configCopy.SMTPPort

	var body bytes.Buffer
	path, err := file.LocateFile("golangp/apps/arx_center/pkg/templates")
	if err != nil {
		log.Fatal("Could not locate template directory", err)
		return
	}
	tmpl, err := ParseTemplateDir(path)
	if err != nil {
		log.Fatal("Could not parse template", err)
		return
	}

	err = tmpl.ExecuteTemplate(&body, emailTemp, &data)
	if err != nil {
		log.Fatalf("Could not execute template %s, %v", emailTemp, err)
		return
	}

	htmlContent := body.String()

	plainContent := html2text.HTML2Text(htmlContent)

	m := gomail.NewMessage()

	m.SetHeader("From", from)
	m.SetHeader("To", to)
	m.SetHeader("Subject", subject)
	m.SetBody("text/plain", plainContent)
	m.AddAlternative("text/html", htmlContent)

	d := gomail.NewDialer(smtpHost, smtpPort, smtpUser, smtpPass)

	if err := d.DialAndSend(m); err != nil {
		log.Fatal("Could not send email: ", err)
	}
}

func SendAccountEmail(user *models.User, data *AccountEmailData, emailTemp string) {

	SendEmail(user.Email, data.Subject, data, emailTemp)
}

func SendShareProjectEmail(emailTo string, data *ShareProjectEmailData, emailTemp string) {

	SendEmail(emailTo, data.Subject, data, emailTemp)
}
