package template_tools

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// ToolResponse represents the response structure for tool functions
type ToolResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    string `json:"data,omitempty"`
}

// TemplateToolsManager manages file operations for template conversion
type TemplateToolsManager struct {
	SourceDir   string // Directory containing the source project
	TemplateDir string // Directory containing the target template
	WorkingDir  string // Working directory for operations
}

// NewTemplateToolsManager creates a new template tools manager
func NewTemplateToolsManager(sourceDir, templateDir, workingDir string) *TemplateToolsManager {
	return &TemplateToolsManager{
		SourceDir:   sourceDir,
		TemplateDir: templateDir,
		WorkingDir:  workingDir,
	}
}

// ListSourceFiles lists files in the source project directory
func (tm *TemplateToolsManager) ListSourceFiles(path string) ToolResponse {
	fullPath := filepath.Join(tm.SourceDir, path)

	// Validate path is within source directory
	if !strings.HasPrefix(fullPath, tm.SourceDir) {
		return ToolResponse{
			Success: false,
			Message: "Path is outside source directory",
		}
	}

	files, err := os.ReadDir(fullPath)
	if err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to list directory: %v", err),
		}
	}

	var fileList []string
	for _, file := range files {
		if file.IsDir() {
			fileList = append(fileList, file.Name()+"/")
		} else {
			fileList = append(fileList, file.Name())
		}
	}

	data, _ := json.Marshal(fileList)
	return ToolResponse{
		Success: true,
		Message: "Files listed successfully",
		Data:    string(data),
	}
}

// ViewSourceFile reads content of source project files
func (tm *TemplateToolsManager) ViewSourceFile(filePath string) ToolResponse {
	fullPath := filepath.Join(tm.SourceDir, filePath)

	// Validate path is within source directory
	if !strings.HasPrefix(fullPath, tm.SourceDir) {
		return ToolResponse{
			Success: false,
			Message: "Path is outside source directory",
		}
	}

	content, err := os.ReadFile(fullPath)
	if err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to read file: %v", err),
		}
	}

	return ToolResponse{
		Success: true,
		Message: "File content retrieved successfully",
		Data:    string(content),
	}
}

// ModifySourceFile replaces entire file content in source project
func (tm *TemplateToolsManager) ModifySourceFile(filePath, content string) ToolResponse {
	fullPath := filepath.Join(tm.SourceDir, filePath)

	// Validate path is within source directory
	if !strings.HasPrefix(fullPath, tm.SourceDir) {
		return ToolResponse{
			Success: false,
			Message: "Path is outside source directory",
		}
	}

	// Create parent directories if they don't exist
	if err := os.MkdirAll(filepath.Dir(fullPath), 0755); err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to create parent directory: %v", err),
		}
	}

	err := os.WriteFile(fullPath, []byte(content), 0644)
	if err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to write file: %v", err),
		}
	}

	return ToolResponse{
		Success: true,
		Message: "File modified successfully",
	}
}

// CreateSourceFile creates new files in source project
func (tm *TemplateToolsManager) CreateSourceFile(filePath, content string) ToolResponse {
	fullPath := filepath.Join(tm.SourceDir, filePath)

	// Validate path is within source directory
	if !strings.HasPrefix(fullPath, tm.SourceDir) {
		return ToolResponse{
			Success: false,
			Message: "Path is outside source directory",
		}
	}

	// Check if file already exists
	if _, err := os.Stat(fullPath); err == nil {
		return ToolResponse{
			Success: false,
			Message: "File already exists",
		}
	}

	// Create parent directories if they don't exist
	if err := os.MkdirAll(filepath.Dir(fullPath), 0755); err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to create parent directory: %v", err),
		}
	}

	err := os.WriteFile(fullPath, []byte(content), 0644)
	if err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to create file: %v", err),
		}
	}

	return ToolResponse{
		Success: true,
		Message: "File created successfully",
	}
}

// DeleteSourceFile deletes files from source project
func (tm *TemplateToolsManager) DeleteSourceFile(filePath string) ToolResponse {
	fullPath := filepath.Join(tm.SourceDir, filePath)

	// Validate path is within source directory
	if !strings.HasPrefix(fullPath, tm.SourceDir) {
		return ToolResponse{
			Success: false,
			Message: "Path is outside source directory",
		}
	}

	err := os.Remove(fullPath)
	if err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to delete file: %v", err),
		}
	}

	return ToolResponse{
		Success: true,
		Message: "File deleted successfully",
	}
}

// ListTargetTemplateFiles lists files in target template directory
func (tm *TemplateToolsManager) ListTargetTemplateFiles(path string) ToolResponse {
	fullPath := filepath.Join(tm.TemplateDir, path)

	// Validate path is within template directory
	if !strings.HasPrefix(fullPath, tm.TemplateDir) {
		return ToolResponse{
			Success: false,
			Message: "Path is outside template directory",
		}
	}

	files, err := os.ReadDir(fullPath)
	if err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to list directory: %v", err),
		}
	}

	var fileList []string
	for _, file := range files {
		if file.IsDir() {
			fileList = append(fileList, file.Name()+"/")
		} else {
			fileList = append(fileList, file.Name())
		}
	}

	data, _ := json.Marshal(fileList)
	return ToolResponse{
		Success: true,
		Message: "Template files listed successfully",
		Data:    string(data),
	}
}

// ViewTargetTemplateFile reads target template file content
func (tm *TemplateToolsManager) ViewTargetTemplateFile(filePath string) ToolResponse {
	fullPath := filepath.Join(tm.TemplateDir, filePath)

	// Validate path is within template directory
	if !strings.HasPrefix(fullPath, tm.TemplateDir) {
		return ToolResponse{
			Success: false,
			Message: "Path is outside template directory",
		}
	}

	content, err := os.ReadFile(fullPath)
	if err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to read template file: %v", err),
		}
	}

	return ToolResponse{
		Success: true,
		Message: "Template file content retrieved successfully",
		Data:    string(content),
	}
}

// CopyFile copies files from target template to source project
func (tm *TemplateToolsManager) CopyFile(sourceFilePath, destinationFilePath string) ToolResponse {
	srcFullPath := filepath.Join(tm.TemplateDir, sourceFilePath)
	dstFullPath := filepath.Join(tm.SourceDir, destinationFilePath)

	// Validate paths
	if !strings.HasPrefix(srcFullPath, tm.TemplateDir) {
		return ToolResponse{
			Success: false,
			Message: "Source path is outside template directory",
		}
	}

	if !strings.HasPrefix(dstFullPath, tm.SourceDir) {
		return ToolResponse{
			Success: false,
			Message: "Destination path is outside source directory",
		}
	}

	// Read source file
	content, err := os.ReadFile(srcFullPath)
	if err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to read source file: %v", err),
		}
	}

	// Create parent directories if they don't exist
	if err := os.MkdirAll(filepath.Dir(dstFullPath), 0755); err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to create parent directory: %v", err),
		}
	}

	// Write to destination
	err = os.WriteFile(dstFullPath, content, 0644)
	if err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to write destination file: %v", err),
		}
	}

	return ToolResponse{
		Success: true,
		Message: "File copied successfully",
	}
}

// CheckCompilation attempts LaTeX compilation and returns logs
func (tm *TemplateToolsManager) CheckCompilation() ToolResponse {
	// Find main .tex file (usually the one that includes others or has \documentclass)
	mainTexFile, err := tm.findMainTexFile()
	if err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to find main .tex file: %v", err),
		}
	}

	// Change to source directory for compilation
	originalDir, err := os.Getwd()
	if err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to get current directory: %v", err),
		}
	}
	defer os.Chdir(originalDir)

	err = os.Chdir(tm.SourceDir)
	if err != nil {
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to change to source directory: %v", err),
		}
	}

	// Run pdflatex compilation
	cmd := exec.Command("pdflatex", "-interaction=nonstopmode", "-halt-on-error", mainTexFile)
	output, err := cmd.CombinedOutput()

	compilationLog := string(output)

	if err != nil {
		return ToolResponse{
			Success: false,
			Message: "LaTeX compilation failed",
			Data:    compilationLog,
		}
	}

	return ToolResponse{
		Success: true,
		Message: "LaTeX compilation successful",
		Data:    compilationLog,
	}
}

// findMainTexFile attempts to find the main .tex file in the source directory
func (tm *TemplateToolsManager) findMainTexFile() (string, error) {
	var texFiles []string

	err := filepath.Walk(tm.SourceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if strings.HasSuffix(strings.ToLower(info.Name()), ".tex") {
			relPath, err := filepath.Rel(tm.SourceDir, path)
			if err != nil {
				return err
			}
			texFiles = append(texFiles, relPath)
		}

		return nil
	})

	if err != nil {
		return "", err
	}

	if len(texFiles) == 0 {
		return "", fmt.Errorf("no .tex files found")
	}

	// Try to find the main file by looking for \documentclass
	for _, texFile := range texFiles {
		content, err := os.ReadFile(filepath.Join(tm.SourceDir, texFile))
		if err != nil {
			continue
		}

		if strings.Contains(string(content), "\\documentclass") {
			return texFile, nil
		}
	}

	// If no main file found with \documentclass, return the first .tex file
	return texFiles[0], nil
}

// CompleteTask marks conversion task as complete (placeholder for now)
func (tm *TemplateToolsManager) CompleteTask() ToolResponse {
	return ToolResponse{
		Success: true,
		Message: "Task marked as complete",
	}
}

// ExecuteTool executes a tool function based on the tool name and parameters
func (tm *TemplateToolsManager) ExecuteTool(toolName string, parameters map[string]interface{}) ToolResponse {
	switch toolName {
	case "list_source_files":
		path, ok := parameters["path"].(string)
		if !ok {
			path = "."
		}
		return tm.ListSourceFiles(path)

	case "view_source_file":
		filePath, ok := parameters["file_path"].(string)
		if !ok {
			return ToolResponse{Success: false, Message: "file_path parameter is required"}
		}
		return tm.ViewSourceFile(filePath)

	case "modify_source_file":
		filePath, ok := parameters["file_path"].(string)
		if !ok {
			return ToolResponse{Success: false, Message: "file_path parameter is required"}
		}
		content, ok := parameters["content"].(string)
		if !ok {
			return ToolResponse{Success: false, Message: "content parameter is required"}
		}
		return tm.ModifySourceFile(filePath, content)

	case "create_source_file":
		filePath, ok := parameters["file_path"].(string)
		if !ok {
			return ToolResponse{Success: false, Message: "file_path parameter is required"}
		}
		content, ok := parameters["content"].(string)
		if !ok {
			return ToolResponse{Success: false, Message: "content parameter is required"}
		}
		return tm.CreateSourceFile(filePath, content)

	case "delete_source_file":
		filePath, ok := parameters["file_path"].(string)
		if !ok {
			return ToolResponse{Success: false, Message: "file_path parameter is required"}
		}
		return tm.DeleteSourceFile(filePath)

	case "list_target_template_files":
		path, ok := parameters["path"].(string)
		if !ok {
			path = "."
		}
		return tm.ListTargetTemplateFiles(path)

	case "view_target_template_file":
		filePath, ok := parameters["file_path"].(string)
		if !ok {
			return ToolResponse{Success: false, Message: "file_path parameter is required"}
		}
		return tm.ViewTargetTemplateFile(filePath)

	case "copy_file":
		sourceFilePath, ok := parameters["source_file_path"].(string)
		if !ok {
			return ToolResponse{Success: false, Message: "source_file_path parameter is required"}
		}
		destinationFilePath, ok := parameters["destination_file_path"].(string)
		if !ok {
			return ToolResponse{Success: false, Message: "destination_file_path parameter is required"}
		}
		return tm.CopyFile(sourceFilePath, destinationFilePath)

	case "check_compilation":
		return tm.CheckCompilation()

	case "complete_task":
		return tm.CompleteTask()

	default:
		return ToolResponse{
			Success: false,
			Message: fmt.Sprintf("Unknown tool: %s", toolName),
		}
	}
}
