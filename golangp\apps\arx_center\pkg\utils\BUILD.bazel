load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "utils",
    srcs = [
        "alipay.go",
        "domain.go",
        "email.go",
        "encode.go",
        "json.go",
        "parse_arr.go",
        "password.go",
        "roomId.go",
        "slice.go",
        "token.go",
    ],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/utils",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/internal/config",
        "//golangp/apps/arx_center/internal/models",
        "//golangp/apps/arx_center/pkg/file",
        "//golangp/apps/arx_center/pkg/logger",
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@com_github_k3a_html2text//:html2text",
        "@com_github_smartwalle_alipay_v3//:alipay",
        "@in_gopkg_gomail_v2//:gomail_v2",
        "@org_golang_x_crypto//bcrypt",
    ],
)
