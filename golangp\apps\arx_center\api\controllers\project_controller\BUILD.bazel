load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "project",
    srcs = [
        "project.controller.go",
        "project_asset.controller.go",
        "project_share.controller.go",
    ],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/api/controllers/project/project_controller",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/internal/config",
        "//golangp/apps/arx_center/internal/models",
        "//golangp/apps/arx_center/internal/services/oss",
        "//golangp/apps/arx_center/pkg/constants",
        "//golangp/apps/arx_center/pkg/logger",
        "//golangp/apps/arx_center/pkg/utils",
        "//golangp/apps/arx_center/pkg/xminio",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@com_github_google_uuid//:uuid",
        "@com_github_minio_minio_go_v7//:minio-go",
        "@io_gorm_gorm//:gorm",
    ],
)

go_library(
    name = "project_controller",
    srcs = [
        "asset_presign.go",
        "asset_update.go",
        "asset_upload.go",
        "project.controller.go",
        "share.go",
    ],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/api/controllers/project_controller",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/internal/config",
        "//golangp/apps/arx_center/internal/models",
        "//golangp/apps/arx_center/internal/services/oss",
        "//golangp/apps/arx_center/pkg/constants",
        "//golangp/apps/arx_center/pkg/logger",
        "//golangp/apps/arx_center/pkg/utils",
        "//golangp/apps/arx_center/pkg/xminio",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@com_github_google_uuid//:uuid",
        "@com_github_minio_minio_go_v7//:minio-go",
        "@io_gorm_gorm//:gorm",
    ],
)
