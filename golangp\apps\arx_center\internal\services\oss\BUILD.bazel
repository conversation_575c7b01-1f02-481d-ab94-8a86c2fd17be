load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "oss",
    srcs = ["main.go"],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/services/oss",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/internal/config",
        "@com_github_minio_minio_go_v7//:minio-go",
        "@com_github_minio_minio_go_v7//pkg/credentials",
    ],
)
