package utils

import (
	"crypto/ecdsa"
	"crypto/elliptic"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/big"
	"time"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/config"
	"github.com/golang-jwt/jwt/v5"
)

func CreateToken(claims jwt.Claims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(config.Config.TokenSecret))
}

func ValidateToken[T jwt.Claims](tokenString string, claims T) error {
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(config.Config.TokenSecret), nil
	})

	if err != nil {
		return err
	}

	date, err := token.Claims.GetExpirationTime()
	if err != nil {
		return fmt.Errorf("failed to get expiration time: %w", err)
	}

	if date != nil {
		if date.Before(time.Now()) {
			return fmt.Errorf("token expired at %s", date)
		}
	}

	return nil
}

type yredisJwk struct {
	Kty    string   `json:"kty"`
	Crv    string   `json:"crv"`
	D      string   `json:"d"`
	X      string   `json:"x"`
	Y      string   `json:"y"`
	KeyOps []string `json:"key_ops"`
	Ext    bool     `json:"ext"`
}

func ToYRedisECDSAPrivateKey(jwkString string) (*ecdsa.PrivateKey, error) {

	var jwk yredisJwk
	if err := json.Unmarshal([]byte(jwkString), &jwk); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JWK: %w", err)
	}

	dBytes, err := base64.RawURLEncoding.DecodeString(jwk.D)
	if err != nil {
		return nil, fmt.Errorf("failed to decode d: %w", err)
	}
	xBytes, err := base64.RawURLEncoding.DecodeString(jwk.X)
	if err != nil {
		return nil, fmt.Errorf("failed to decode x: %w", err)
	}
	yBytes, err := base64.RawURLEncoding.DecodeString(jwk.Y)
	if err != nil {
		return nil, fmt.Errorf("failed to decode y: %w", err)
	}

	d := new(big.Int).SetBytes(dBytes)
	x := new(big.Int).SetBytes(xBytes)
	y := new(big.Int).SetBytes(yBytes)

	var curve elliptic.Curve
	switch jwk.Crv {
	case "P-256":
		curve = elliptic.P256()
	case "P-384":
		curve = elliptic.P384()
	case "P-521":
		curve = elliptic.P521()
	default:
		return nil, fmt.Errorf("unsupported curve: %s", jwk.Crv)
	}

	privateKey := &ecdsa.PrivateKey{
		PublicKey: ecdsa.PublicKey{
			Curve: curve,
			X:     x,
			Y:     y,
		},
		D: d,
	}

	return privateKey, nil
}

func CreateYRedisToken(ttl time.Duration, appName string, yuserid string, privateKey *ecdsa.PrivateKey) (string, error) {

	now := time.Now()

	claims := make(jwt.MapClaims)
	claims["iss"] = appName
	claims["exp"] = now.Add(ttl).UnixNano() / int64(time.Millisecond)
	claims["yuserid"] = yuserid

	token, err := jwt.NewWithClaims(jwt.SigningMethodES384, claims).SignedString(privateKey)
	if err != nil {
		return "", fmt.Errorf("create: sign token: %w", err)
	}

	return token, nil
}
