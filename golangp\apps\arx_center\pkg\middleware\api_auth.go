package middleware

import (
	"net/http"
	"strings"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/config"
	"github.com/gin-gonic/gin"
)

// APIAuth middleware validates API_SECRET authorization header
func APIAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// Get the Authorization header
		authHeader := ctx.GetHeader("Authorization")

		// Check if Authorization header is present
		if authHeader == "" {
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"status":  "fail",
				"message": "Authorization header is required",
			})
			return
		}

		// Extract the API secret from the Authorization header
		// Expected format: "Bearer <api_secret>" or just "<api_secret>"
		var providedSecret string
		if strings.HasPrefix(authHeader, "Bearer ") {
			providedSecret = strings.TrimPrefix(authHeader, "Bearer ")
		} else {
			providedSecret = authHeader
		}

		// Get the configured API secret
		configuredSecret := config.Config.APISecret

		// Check if API secret is configured
		if configuredSecret == "" {
			ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"status":  "error",
				"message": "API secret is not configured",
			})
			return
		}

		// Validate the provided API secret against the configured one
		if providedSecret != configuredSecret {
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"status":  "fail",
				"message": "Invalid API secret",
			})
			return
		}

		// If validation passes, continue to the next handler
		ctx.Next()
	}
}

// APIAuthOptional middleware validates API_SECRET authorization header but allows requests without it
// This can be useful for endpoints that have both public and private access
func APIAuthOptional() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// Get the Authorization header
		authHeader := ctx.GetHeader("Authorization")

		// If no Authorization header is present, continue without validation
		if authHeader == "" {
			ctx.Next()
			return
		}

		// Extract the API secret from the Authorization header
		var providedSecret string
		if strings.HasPrefix(authHeader, "Bearer ") {
			providedSecret = strings.TrimPrefix(authHeader, "Bearer ")
		} else {
			providedSecret = authHeader
		}

		// Get the configured API secret
		configuredSecret := config.Config.APISecret

		// Check if API secret is configured
		if configuredSecret == "" {
			ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"status":  "error",
				"message": "API secret is not configured",
			})
			return
		}

		// Validate the provided API secret against the configured one
		if providedSecret != configuredSecret {
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"status":  "fail",
				"message": "Invalid API secret",
			})
			return
		}

		// Set a flag to indicate that API auth was successful
		ctx.Set("api_authenticated", true)

		// If validation passes, continue to the next handler
		ctx.Next()
	}
}
