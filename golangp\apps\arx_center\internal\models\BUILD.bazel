load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "models",
    srcs = [
        "common.model.go",
        "project.model.go",
        "project_asset.model.go",
        "project_member.model.go",
        "template.model.go",
        "template_conversion.model.go",
        "user.model.go",
    ],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/models",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/pkg/constants",
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@com_github_google_uuid//:uuid",
        "@io_gorm_gorm//:gorm",
    ],
)
