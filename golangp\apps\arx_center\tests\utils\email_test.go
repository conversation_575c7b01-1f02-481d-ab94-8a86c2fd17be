package utils

import (
	"log"
	"os"
	"testing"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/internal/config"

	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/utils"
)

func TestSendAccountEmail(t *testing.T) {
	err := os.Chdir("/home/<USER>/Einstein")
	if err != nil {
		panic(err)
	}

	err = config.LoadConfig("config/settings-dev.yml")
	if err != nil {
		log.Fatalf("Load failed: %v\n", err)
		return
	}

	emailData := utils.AccountEmailData{
		URL:              "https://arxtect.com",
		VerificationCode: "123456",
		FirstName:        "test",
		Subject:          "Welcome to our service for test",
	}

	// Call the function
	utils.SendEmail("<EMAIL>", emailData.Subject, emailData, "resetPassword.html")
}
