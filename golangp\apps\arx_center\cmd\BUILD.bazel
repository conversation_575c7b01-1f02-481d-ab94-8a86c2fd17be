load("@rules_go//go:def.bzl", "go_binary", "go_library")

go_binary(
    name = "arx_center",
    args = [
        "server",
        "-c",
        "configs/settings.env",
    ],
    embed = [":cmd"],
    visibility = ["//visibility:public"],
)

go_binary(
    name = "migrate",
    args = [
        "migrate",
        "-c",
        "configs/settings.env",
    ],
    embed = [":cmd"],
    visibility = ["//visibility:public"],
)

go_library(
    name = "cmd",
    srcs = ["main.go"],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/cmd",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/cmd/config",
        "//golangp/apps/arx_center/cmd/migration",
        "//golangp/apps/arx_center/cmd/server",
        "//golangp/apps/arx_center/cmd/version",
        "@com_github_spf13_cobra//:cobra",
    ],
)
