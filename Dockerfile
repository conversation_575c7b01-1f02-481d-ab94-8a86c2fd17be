FROM golang:1.24.3-alpine as build-stage


WORKDIR /workspace
RUN go env -w CGO_ENABLED=0
# If needed, uncomment the following line to set the GOPROXY
# RUN go env -w GOPROXY=https://goproxy.cn,direct 
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o ArxCenter golangp/apps/arx_center/cmd/main.go

FROM alpine
WORKDIR /app
COPY --from=build-stage /workspace/ArxCenter   ArxCenter 
COPY --from=build-stage /workspace/golangp/apps/arx_center/pkg/templates  golangp/apps/arx_center/pkg/templates


EXPOSE 8012
RUN  chmod +x ArxCenter 
CMD ["ArxCenter","server","-c", "configs/settings.yml"]