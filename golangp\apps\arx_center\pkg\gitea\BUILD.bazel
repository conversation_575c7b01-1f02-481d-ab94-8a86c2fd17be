load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "gitea",
    srcs = [
        "admin.client.go",
        "admin.create.go",
        "user.client.go",
        "user.repo.go",
        "user.token.go",
    ],
    importpath = "github.com/Arxtect/ArxBackend/golangp/apps/arx_center/pkg/gitea",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/internal/config",
        "//golangp/apps/arx_center/pkg/constants",
        "@io_gitea_code_sdk_gitea//:gitea",
    ],
)
