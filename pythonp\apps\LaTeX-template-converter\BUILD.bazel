load("@pip//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_binary")

py_binary(
    name = "api_service",
    srcs = [
        "api-service.py",
        "function.py",
        "main.py",
        "targetTemplateMainTexMapping.py",
        "targetTemplateRecCompileMapping.py",
    ],
    data = glob(["templates/*.zip"]) + [
        "targetTemplateMainTexMapping.py",
        "targetTemplateRecCompileMapping.py",
    ],
    imports = ["."],
    main = "api-service.py",
    visibility = ["//visibility:public"],
    deps = [
        requirement("fastapi"),
        requirement("uvicorn"),
        requirement("python-multipart"),
    ],
)
