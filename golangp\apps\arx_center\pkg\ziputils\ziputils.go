package ziputils

import (
	"archive/zip"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
)

// ExtractZip extracts a ZIP file to a target directory, removing __MACOSX folders
func ExtractZip(zipFilePath, targetDir string) error {
	// Open the ZIP file for reading
	reader, err := zip.OpenReader(zipFilePath)
	if err != nil {
		return fmt.Errorf("failed to open ZIP file: %w", err)
	}
	defer reader.Close()

	// Create target directory if it doesn't exist
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		return fmt.Errorf("failed to create target directory: %w", err)
	}

	// Extract files
	for _, file := range reader.File {
		// Skip __MACOSX folders and files
		if strings.Contains(file.Name, "__MACOSX") {
			continue
		}

		// Validate file path to prevent directory traversal attacks
		destPath, err := filepath.Rel(targetDir, file.Name)
		if err != nil {
			return fmt.Errorf("invalid file path: %v", err)
		}

		if file.FileInfo().IsDir() {
			// Create directory
			if err := os.MkdirAll(destPath, file.FileInfo().Mode()); err != nil {
				return fmt.Errorf("failed to create directory %s: %w", destPath, err)
			}
			continue
		}

		// Create parent directories if they don't exist
		if err := os.MkdirAll(filepath.Dir(destPath), 0755); err != nil {
			return fmt.Errorf("failed to create parent directory for %s: %w", destPath, err)
		}

		// Extract file
		if err := extractFile(file, destPath); err != nil {
			return fmt.Errorf("failed to extract file %s: %w", file.Name, err)
		}
	}

	return nil
}

// extractFile extracts a single file from the ZIP archive
func extractFile(file *zip.File, destPath string) error {
	// Open file in ZIP
	rc, err := file.Open()
	if err != nil {
		return err
	}
	defer rc.Close()

	// Create destination file
	destFile, err := os.OpenFile(destPath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.FileInfo().Mode())
	if err != nil {
		return err
	}
	defer destFile.Close()

	// Copy file contents
	_, err = io.Copy(destFile, rc)
	return err
}

// CreateZip creates a ZIP file from a directory
func CreateZip(sourceDir, zipFilePath string) error {
	// Create ZIP file
	zipFile, err := os.Create(zipFilePath)
	if err != nil {
		return fmt.Errorf("failed to create ZIP file: %w", err)
	}
	defer zipFile.Close()

	// Create ZIP writer
	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	// Walk through source directory
	return filepath.Walk(sourceDir, func(filePath string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip __MACOSX folders
		if strings.Contains(filePath, "__MACOSX") {
			if info.IsDir() {
				return filepath.SkipDir
			}
			return nil
		}

		// Get relative path
		relPath, err := filepath.Rel(sourceDir, filePath)
		if err != nil {
			return err
		}

		// Skip root directory
		if relPath == "." {
			return nil
		}

		// Create ZIP header
		header, err := zip.FileInfoHeader(info)
		if err != nil {
			return err
		}

		// Set header name to relative path
		header.Name = filepath.ToSlash(relPath)

		// Handle directories
		if info.IsDir() {
			header.Name += "/"
			_, err := zipWriter.CreateHeader(header)
			return err
		}

		// Handle files
		header.Method = zip.Deflate
		writer, err := zipWriter.CreateHeader(header)
		if err != nil {
			return err
		}

		// Open source file
		file, err := os.Open(filePath)
		if err != nil {
			return err
		}
		defer file.Close()

		// Copy file contents to ZIP
		_, err = io.Copy(writer, file)
		return err
	})
}

// ListZipContents returns a list of files in a ZIP archive
func ListZipContents(zipFilePath string) ([]string, error) {
	reader, err := zip.OpenReader(zipFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open ZIP file: %w", err)
	}
	defer reader.Close()

	var files []string
	for _, file := range reader.File {
		// Skip __MACOSX folders
		if strings.Contains(file.Name, "__MACOSX") {
			continue
		}
		files = append(files, file.Name)
	}

	return files, nil
}

// ValidateZipFile validates that a ZIP file is valid and safe to extract
func ValidateZipFile(zipFilePath string) error {
	reader, err := zip.OpenReader(zipFilePath)
	if err != nil {
		return fmt.Errorf("failed to open ZIP file: %w", err)
	}
	defer reader.Close()

	for _, file := range reader.File {
		// Check for directory traversal attempts
		if strings.Contains(file.Name, "..") {
			return fmt.Errorf("unsafe file path detected: %s", file.Name)
		}

		// Check for absolute paths
		if filepath.IsAbs(file.Name) {
			return fmt.Errorf("absolute file path detected: %s", file.Name)
		}
	}

	return nil
}

// GetZipFileInfo returns information about a ZIP file
type ZipFileInfo struct {
	TotalFiles     int
	TotalSize      int64
	CompressedSize int64
	HasTexFiles    bool
	TexFiles       []string
	TopLevelDirs   []string
}

// GetZipInfo analyzes a ZIP file and returns information about its contents
func GetZipInfo(zipFilePath string) (*ZipFileInfo, error) {
	reader, err := zip.OpenReader(zipFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open ZIP file: %w", err)
	}
	defer reader.Close()

	info := &ZipFileInfo{
		TexFiles:     make([]string, 0),
		TopLevelDirs: make([]string, 0),
	}

	topLevelDirsMap := make(map[string]bool)

	for _, file := range reader.File {
		// Skip __MACOSX folders
		if strings.Contains(file.Name, "__MACOSX") {
			continue
		}

		info.TotalFiles++
		info.TotalSize += int64(file.UncompressedSize64)
		info.CompressedSize += int64(file.CompressedSize64)

		// Check for .tex files
		if strings.HasSuffix(strings.ToLower(file.Name), ".tex") {
			info.HasTexFiles = true
			info.TexFiles = append(info.TexFiles, file.Name)
		}

		// Track top-level directories
		parts := strings.Split(file.Name, "/")
		if len(parts) > 1 {
			topLevelDirsMap[parts[0]] = true
		}
	}

	// Convert map to slice
	for dir := range topLevelDirsMap {
		info.TopLevelDirs = append(info.TopLevelDirs, dir)
	}

	return info, nil
}

// CleanupTempDir removes a temporary directory and all its contents
func CleanupTempDir(dirPath string) error {
	rel, err := filepath.Rel(os.TempDir(), dirPath)
	if err != nil || rel == ".." || strings.HasPrefix(rel, "../") {
		return fmt.Errorf("path not allowed for cleanup: %s", dirPath)
	}
	return os.RemoveAll(dirPath)
}
