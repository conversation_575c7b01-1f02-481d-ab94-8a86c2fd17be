package routes

import (
	"github.com/Arxtect/ArxBackend/golangp/apps/arx_center/api/controllers"

	"github.com/gin-gonic/gin"
)

type AuthRouteController struct {
	authController controllers.AuthController
}

func NewAuthRouteController(authController controllers.AuthController) AuthRouteController {
	return AuthRouteController{authController}
}

func (rc *AuthRouteController) AuthRoute(rg *gin.RouterGroup) {
	router := rg.Group("auth")

	router.POST("/register", rc.authController.SignUpUser)
	router.POST("/verify-email", rc.authController.VerifyEmail)
	router.POST("/login", rc.authController.SignInUser)
	router.POST("/logout", rc.authController.LogoutUser)
	router.POST("/forgot-password", rc.authController.ForgotPassword)
	router.POST("/reset-password", rc.authController.ResetPassword)
}
